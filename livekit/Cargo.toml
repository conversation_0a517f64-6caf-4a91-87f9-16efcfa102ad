[package]
name = "livekit"
version = "0.7.14"
edition = "2021"
license = "Apache-2.0"
description = "Rust Client SDK for LiveKit"
repository = "https://github.com/livekit/rust-sdks"

[features]
# By default ws TLS is not enabled
default = ["tokio"]

async = ["livekit-api/signal-client-async"]
tokio = ["livekit-api/signal-client-tokio"]
dispatcher = ["livekit-api/signal-client-dispatcher"]


# Note that the following features only change the behavior of tokio-tungstenite.
# It doesn't change the behavior of libwebrtc/webrtc-sys
native-tls = ["livekit-api/native-tls"]
native-tls-vendored = ["livekit-api/native-tls-vendored"]
rustls-tls-native-roots = ["livekit-api/rustls-tls-native-roots"]
rustls-tls-webpki-roots = ["livekit-api/rustls-tls-webpki-roots"]
__rustls-tls = ["livekit-api/__rustls-tls"]

# internal features (used by livekit-ffi)
__lk-internal = []

[dependencies]
livekit-runtime = { workspace = true, default-features = false }
livekit-api = { workspace = true, default-features = false }
libwebrtc = { workspace = true }
livekit-protocol = { workspace = true }
prost = "0.12"
serde = { version = "1", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1", default-features = false, features = ["sync", "macros", "fs"] }
parking_lot = { version = "0.12" }
futures-util = { version = "0.3", default-features = false, features = ["sink"] }
thiserror = "1.0"
lazy_static = "1.4"
log = "0.4"
chrono = "0.4.38"
semver = "1.0"
libloading = { version = "0.8.6" }
bytes = "1.10.1"
bmrng = "0.5.2"
