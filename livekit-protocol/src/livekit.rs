// @generated
// This file is @generated by prost-build.
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct MetricsBatch {
    /// time at which this batch is sent based on a monotonic clock (millisecond resolution)
    #[prost(int64, tag="1")]
    pub timestamp_ms: i64,
    #[prost(message, optional, tag="2")]
    pub normalized_timestamp: ::core::option::Option<::pbjson_types::Timestamp>,
    /// To avoid repeating string values, we store them in a separate list and reference them by index
    /// This is useful for storing participant identities, track names, etc.
    /// There is also a predefined list of labels that can be used to reference common metrics.
    /// They have reserved indices from 0 to (METRIC_LABEL_PREDEFINED_MAX_VALUE - 1).
    /// Indexes pointing at str_data should start from METRIC_LABEL_PREDEFINED_MAX_VALUE, 
    /// such that str_data\[0\] == index of METRIC_LABEL_PREDEFINED_MAX_VALUE.
    #[prost(string, repeated, tag="3")]
    pub str_data: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(message, repeated, tag="4")]
    pub time_series: ::prost::alloc::vec::Vec<TimeSeriesMetric>,
    #[prost(message, repeated, tag="5")]
    pub events: ::prost::alloc::vec::Vec<EventMetric>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TimeSeriesMetric {
    /// Metric name e.g "speech_probablity". The string value is not directly stored in the message, but referenced by index
    /// in the `str_data` field of `MetricsBatch`
    #[prost(uint32, tag="1")]
    pub label: u32,
    /// index into `str_data`
    #[prost(uint32, tag="2")]
    pub participant_identity: u32,
    /// index into `str_data`
    #[prost(uint32, tag="3")]
    pub track_sid: u32,
    #[prost(message, repeated, tag="4")]
    pub samples: ::prost::alloc::vec::Vec<MetricSample>,
    /// index into 'str_data'
    #[prost(uint32, tag="5")]
    pub rid: u32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct MetricSample {
    /// time of metric based on a monotonic clock (in milliseconds)
    #[prost(int64, tag="1")]
    pub timestamp_ms: i64,
    #[prost(message, optional, tag="2")]
    pub normalized_timestamp: ::core::option::Option<::pbjson_types::Timestamp>,
    #[prost(float, tag="3")]
    pub value: f32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct EventMetric {
    #[prost(uint32, tag="1")]
    pub label: u32,
    /// index into `str_data`
    #[prost(uint32, tag="2")]
    pub participant_identity: u32,
    /// index into `str_data`
    #[prost(uint32, tag="3")]
    pub track_sid: u32,
    /// start time of event based on a monotonic clock (in milliseconds)
    #[prost(int64, tag="4")]
    pub start_timestamp_ms: i64,
    /// end time of event based on a monotonic clock (in milliseconds), if needed
    #[prost(int64, optional, tag="5")]
    pub end_timestamp_ms: ::core::option::Option<i64>,
    #[prost(message, optional, tag="6")]
    pub normalized_start_timestamp: ::core::option::Option<::pbjson_types::Timestamp>,
    #[prost(message, optional, tag="7")]
    pub normalized_end_timestamp: ::core::option::Option<::pbjson_types::Timestamp>,
    #[prost(string, tag="8")]
    pub metadata: ::prost::alloc::string::String,
    /// index into 'str_data'
    #[prost(uint32, tag="9")]
    pub rid: u32,
}
//
// Protocol used to record metrics for a specific session.
//
// Clients send their timestamp in their own monotonically increasing time (e.g `performance.now` on JS).
// These timestamps are then augmented by the SFU to its time base.
//
// A metric can be linked to a specific track by setting `track_sid`.

/// index from [0: MAX_LABEL_PREDEFINED_MAX_VALUE) are for predefined labels (`MetricLabel`)
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum MetricLabel {
    /// time to first token from LLM
    AgentsLlmTtft = 0,
    /// time to final transcription
    AgentsSttTtft = 1,
    /// time to first byte
    AgentsTtsTtfb = 2,
    /// Number of video freezes
    ClientVideoSubscriberFreezeCount = 3,
    /// total duration of freezes
    ClientVideoSubscriberTotalFreezeDuration = 4,
    /// number of video pauses
    ClientVideoSubscriberPauseCount = 5,
    /// total duration of pauses
    ClientVideoSubscriberTotalPausesDuration = 6,
    /// number of concealed (synthesized) audio samples
    ClientAudioSubscriberConcealedSamples = 7,
    /// number of silent concealed samples
    ClientAudioSubscriberSilentConcealedSamples = 8,
    /// number of concealment events
    ClientAudioSubscriberConcealmentEvents = 9,
    /// number of interruptions
    ClientAudioSubscriberInterruptionCount = 10,
    /// total duration of interruptions
    ClientAudioSubscriberTotalInterruptionDuration = 11,
    /// total time spent in jitter buffer
    ClientSubscriberJitterBufferDelay = 12,
    /// total time spent in jitter buffer
    ClientSubscriberJitterBufferEmittedCount = 13,
    /// total duration spent in bandwidth quality limitation
    ClientVideoPublisherQualityLimitationDurationBandwidth = 14,
    /// total duration spent in cpu quality limitation
    ClientVideoPublisherQualityLimitationDurationCpu = 15,
    /// total duration spent in other quality limitation
    ClientVideoPublisherQualityLimitationDurationOther = 16,
    /// Publisher RTT (participant -> server)
    PublisherRtt = 17,
    /// RTT between publisher node and subscriber node (could involve intermedia node(s))
    ServerMeshRtt = 18,
    /// Subscribe RTT (server -> participant)
    SubscriberRtt = 19,
    PredefinedMaxValue = 4096,
}
impl MetricLabel {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            MetricLabel::AgentsLlmTtft => "AGENTS_LLM_TTFT",
            MetricLabel::AgentsSttTtft => "AGENTS_STT_TTFT",
            MetricLabel::AgentsTtsTtfb => "AGENTS_TTS_TTFB",
            MetricLabel::ClientVideoSubscriberFreezeCount => "CLIENT_VIDEO_SUBSCRIBER_FREEZE_COUNT",
            MetricLabel::ClientVideoSubscriberTotalFreezeDuration => "CLIENT_VIDEO_SUBSCRIBER_TOTAL_FREEZE_DURATION",
            MetricLabel::ClientVideoSubscriberPauseCount => "CLIENT_VIDEO_SUBSCRIBER_PAUSE_COUNT",
            MetricLabel::ClientVideoSubscriberTotalPausesDuration => "CLIENT_VIDEO_SUBSCRIBER_TOTAL_PAUSES_DURATION",
            MetricLabel::ClientAudioSubscriberConcealedSamples => "CLIENT_AUDIO_SUBSCRIBER_CONCEALED_SAMPLES",
            MetricLabel::ClientAudioSubscriberSilentConcealedSamples => "CLIENT_AUDIO_SUBSCRIBER_SILENT_CONCEALED_SAMPLES",
            MetricLabel::ClientAudioSubscriberConcealmentEvents => "CLIENT_AUDIO_SUBSCRIBER_CONCEALMENT_EVENTS",
            MetricLabel::ClientAudioSubscriberInterruptionCount => "CLIENT_AUDIO_SUBSCRIBER_INTERRUPTION_COUNT",
            MetricLabel::ClientAudioSubscriberTotalInterruptionDuration => "CLIENT_AUDIO_SUBSCRIBER_TOTAL_INTERRUPTION_DURATION",
            MetricLabel::ClientSubscriberJitterBufferDelay => "CLIENT_SUBSCRIBER_JITTER_BUFFER_DELAY",
            MetricLabel::ClientSubscriberJitterBufferEmittedCount => "CLIENT_SUBSCRIBER_JITTER_BUFFER_EMITTED_COUNT",
            MetricLabel::ClientVideoPublisherQualityLimitationDurationBandwidth => "CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_BANDWIDTH",
            MetricLabel::ClientVideoPublisherQualityLimitationDurationCpu => "CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_CPU",
            MetricLabel::ClientVideoPublisherQualityLimitationDurationOther => "CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_OTHER",
            MetricLabel::PublisherRtt => "PUBLISHER_RTT",
            MetricLabel::ServerMeshRtt => "SERVER_MESH_RTT",
            MetricLabel::SubscriberRtt => "SUBSCRIBER_RTT",
            MetricLabel::PredefinedMaxValue => "METRIC_LABEL_PREDEFINED_MAX_VALUE",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "AGENTS_LLM_TTFT" => Some(Self::AgentsLlmTtft),
            "AGENTS_STT_TTFT" => Some(Self::AgentsSttTtft),
            "AGENTS_TTS_TTFB" => Some(Self::AgentsTtsTtfb),
            "CLIENT_VIDEO_SUBSCRIBER_FREEZE_COUNT" => Some(Self::ClientVideoSubscriberFreezeCount),
            "CLIENT_VIDEO_SUBSCRIBER_TOTAL_FREEZE_DURATION" => Some(Self::ClientVideoSubscriberTotalFreezeDuration),
            "CLIENT_VIDEO_SUBSCRIBER_PAUSE_COUNT" => Some(Self::ClientVideoSubscriberPauseCount),
            "CLIENT_VIDEO_SUBSCRIBER_TOTAL_PAUSES_DURATION" => Some(Self::ClientVideoSubscriberTotalPausesDuration),
            "CLIENT_AUDIO_SUBSCRIBER_CONCEALED_SAMPLES" => Some(Self::ClientAudioSubscriberConcealedSamples),
            "CLIENT_AUDIO_SUBSCRIBER_SILENT_CONCEALED_SAMPLES" => Some(Self::ClientAudioSubscriberSilentConcealedSamples),
            "CLIENT_AUDIO_SUBSCRIBER_CONCEALMENT_EVENTS" => Some(Self::ClientAudioSubscriberConcealmentEvents),
            "CLIENT_AUDIO_SUBSCRIBER_INTERRUPTION_COUNT" => Some(Self::ClientAudioSubscriberInterruptionCount),
            "CLIENT_AUDIO_SUBSCRIBER_TOTAL_INTERRUPTION_DURATION" => Some(Self::ClientAudioSubscriberTotalInterruptionDuration),
            "CLIENT_SUBSCRIBER_JITTER_BUFFER_DELAY" => Some(Self::ClientSubscriberJitterBufferDelay),
            "CLIENT_SUBSCRIBER_JITTER_BUFFER_EMITTED_COUNT" => Some(Self::ClientSubscriberJitterBufferEmittedCount),
            "CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_BANDWIDTH" => Some(Self::ClientVideoPublisherQualityLimitationDurationBandwidth),
            "CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_CPU" => Some(Self::ClientVideoPublisherQualityLimitationDurationCpu),
            "CLIENT_VIDEO_PUBLISHER_QUALITY_LIMITATION_DURATION_OTHER" => Some(Self::ClientVideoPublisherQualityLimitationDurationOther),
            "PUBLISHER_RTT" => Some(Self::PublisherRtt),
            "SERVER_MESH_RTT" => Some(Self::ServerMeshRtt),
            "SUBSCRIBER_RTT" => Some(Self::SubscriberRtt),
            "METRIC_LABEL_PREDEFINED_MAX_VALUE" => Some(Self::PredefinedMaxValue),
            _ => None,
        }
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Pagination {
    /// list entities which IDs are greater
    #[prost(string, tag="1")]
    pub after_id: ::prost::alloc::string::String,
    #[prost(int32, tag="2")]
    pub limit: i32,
}
/// ListUpdate is used for updated APIs where 'repeated string' field is modified.
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListUpdate {
    /// set the field to a new list
    #[prost(string, repeated, tag="1")]
    pub set: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Room {
    #[prost(string, tag="1")]
    pub sid: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub name: ::prost::alloc::string::String,
    #[prost(uint32, tag="3")]
    pub empty_timeout: u32,
    #[prost(uint32, tag="14")]
    pub departure_timeout: u32,
    #[prost(uint32, tag="4")]
    pub max_participants: u32,
    #[prost(int64, tag="5")]
    pub creation_time: i64,
    #[prost(int64, tag="15")]
    pub creation_time_ms: i64,
    #[prost(string, tag="6")]
    pub turn_password: ::prost::alloc::string::String,
    #[prost(message, repeated, tag="7")]
    pub enabled_codecs: ::prost::alloc::vec::Vec<Codec>,
    #[prost(string, tag="8")]
    pub metadata: ::prost::alloc::string::String,
    #[prost(uint32, tag="9")]
    pub num_participants: u32,
    #[prost(uint32, tag="11")]
    pub num_publishers: u32,
    #[prost(bool, tag="10")]
    pub active_recording: bool,
    #[prost(message, optional, tag="13")]
    pub version: ::core::option::Option<TimedVersion>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Codec {
    #[prost(string, tag="1")]
    pub mime: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub fmtp_line: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct PlayoutDelay {
    #[prost(bool, tag="1")]
    pub enabled: bool,
    #[prost(uint32, tag="2")]
    pub min: u32,
    #[prost(uint32, tag="3")]
    pub max: u32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ParticipantPermission {
    /// allow participant to subscribe to other tracks in the room
    #[prost(bool, tag="1")]
    pub can_subscribe: bool,
    /// allow participant to publish new tracks to room
    #[prost(bool, tag="2")]
    pub can_publish: bool,
    /// allow participant to publish data
    #[prost(bool, tag="3")]
    pub can_publish_data: bool,
    /// sources that are allowed to be published
    #[prost(enumeration="TrackSource", repeated, tag="9")]
    pub can_publish_sources: ::prost::alloc::vec::Vec<i32>,
    /// indicates that it's hidden to others
    #[prost(bool, tag="7")]
    pub hidden: bool,
    /// indicates it's a recorder instance
    /// deprecated: use ParticipantInfo.kind instead
    #[deprecated]
    #[prost(bool, tag="8")]
    pub recorder: bool,
    /// indicates that participant can update own metadata and attributes
    #[prost(bool, tag="10")]
    pub can_update_metadata: bool,
    /// indicates that participant is an agent
    /// deprecated: use ParticipantInfo.kind instead
    #[deprecated]
    #[prost(bool, tag="11")]
    pub agent: bool,
    /// if a participant can subscribe to metrics
    #[prost(bool, tag="12")]
    pub can_subscribe_metrics: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ParticipantInfo {
    #[prost(string, tag="1")]
    pub sid: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub identity: ::prost::alloc::string::String,
    #[prost(enumeration="participant_info::State", tag="3")]
    pub state: i32,
    #[prost(message, repeated, tag="4")]
    pub tracks: ::prost::alloc::vec::Vec<TrackInfo>,
    #[prost(string, tag="5")]
    pub metadata: ::prost::alloc::string::String,
    /// timestamp when participant joined room, in seconds
    #[prost(int64, tag="6")]
    pub joined_at: i64,
    /// timestamp when participant joined room, in milliseconds
    #[prost(int64, tag="17")]
    pub joined_at_ms: i64,
    #[prost(string, tag="9")]
    pub name: ::prost::alloc::string::String,
    #[prost(uint32, tag="10")]
    pub version: u32,
    #[prost(message, optional, tag="11")]
    pub permission: ::core::option::Option<ParticipantPermission>,
    #[prost(string, tag="12")]
    pub region: ::prost::alloc::string::String,
    /// indicates the participant has an active publisher connection
    /// and can publish to the server
    #[prost(bool, tag="13")]
    pub is_publisher: bool,
    #[prost(enumeration="participant_info::Kind", tag="14")]
    pub kind: i32,
    #[prost(map="string, string", tag="15")]
    pub attributes: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    #[prost(enumeration="DisconnectReason", tag="16")]
    pub disconnect_reason: i32,
    #[prost(enumeration="participant_info::KindDetail", repeated, tag="18")]
    pub kind_details: ::prost::alloc::vec::Vec<i32>,
}
/// Nested message and enum types in `ParticipantInfo`.
pub mod participant_info {
    #[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
    #[repr(i32)]
    pub enum State {
        /// websocket' connected, but not offered yet
        Joining = 0,
        /// server received client offer
        Joined = 1,
        /// ICE connectivity established
        Active = 2,
        /// WS disconnected
        Disconnected = 3,
    }
    impl State {
        /// String value of the enum field names used in the ProtoBuf definition.
        ///
        /// The values are not transformed in any way and thus are considered stable
        /// (if the ProtoBuf definition does not change) and safe for programmatic use.
        pub fn as_str_name(&self) -> &'static str {
            match self {
                State::Joining => "JOINING",
                State::Joined => "JOINED",
                State::Active => "ACTIVE",
                State::Disconnected => "DISCONNECTED",
            }
        }
        /// Creates an enum from field names used in the ProtoBuf definition.
        pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
            match value {
                "JOINING" => Some(Self::Joining),
                "JOINED" => Some(Self::Joined),
                "ACTIVE" => Some(Self::Active),
                "DISCONNECTED" => Some(Self::Disconnected),
                _ => None,
            }
        }
    }
    #[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
    #[repr(i32)]
    pub enum Kind {
        /// standard participants, e.g. web clients
        Standard = 0,
        /// only ingests streams
        Ingress = 1,
        /// only consumes streams
        Egress = 2,
        /// SIP participants
        Sip = 3,
        /// LiveKit agents
        Agent = 4,
    }
    impl Kind {
        /// String value of the enum field names used in the ProtoBuf definition.
        ///
        /// The values are not transformed in any way and thus are considered stable
        /// (if the ProtoBuf definition does not change) and safe for programmatic use.
        pub fn as_str_name(&self) -> &'static str {
            match self {
                Kind::Standard => "STANDARD",
                Kind::Ingress => "INGRESS",
                Kind::Egress => "EGRESS",
                Kind::Sip => "SIP",
                Kind::Agent => "AGENT",
            }
        }
        /// Creates an enum from field names used in the ProtoBuf definition.
        pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
            match value {
                "STANDARD" => Some(Self::Standard),
                "INGRESS" => Some(Self::Ingress),
                "EGRESS" => Some(Self::Egress),
                "SIP" => Some(Self::Sip),
                "AGENT" => Some(Self::Agent),
                _ => None,
            }
        }
    }
    #[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
    #[repr(i32)]
    pub enum KindDetail {
        CloudAgent = 0,
        Forwarded = 1,
    }
    impl KindDetail {
        /// String value of the enum field names used in the ProtoBuf definition.
        ///
        /// The values are not transformed in any way and thus are considered stable
        /// (if the ProtoBuf definition does not change) and safe for programmatic use.
        pub fn as_str_name(&self) -> &'static str {
            match self {
                KindDetail::CloudAgent => "CLOUD_AGENT",
                KindDetail::Forwarded => "FORWARDED",
            }
        }
        /// Creates an enum from field names used in the ProtoBuf definition.
        pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
            match value {
                "CLOUD_AGENT" => Some(Self::CloudAgent),
                "FORWARDED" => Some(Self::Forwarded),
                _ => None,
            }
        }
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Encryption {
}
/// Nested message and enum types in `Encryption`.
pub mod encryption {
    #[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
    #[repr(i32)]
    pub enum Type {
        None = 0,
        Gcm = 1,
        Custom = 2,
    }
    impl Type {
        /// String value of the enum field names used in the ProtoBuf definition.
        ///
        /// The values are not transformed in any way and thus are considered stable
        /// (if the ProtoBuf definition does not change) and safe for programmatic use.
        pub fn as_str_name(&self) -> &'static str {
            match self {
                Type::None => "NONE",
                Type::Gcm => "GCM",
                Type::Custom => "CUSTOM",
            }
        }
        /// Creates an enum from field names used in the ProtoBuf definition.
        pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
            match value {
                "NONE" => Some(Self::None),
                "GCM" => Some(Self::Gcm),
                "CUSTOM" => Some(Self::Custom),
                _ => None,
            }
        }
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SimulcastCodecInfo {
    #[prost(string, tag="1")]
    pub mime_type: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub mid: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub cid: ::prost::alloc::string::String,
    #[prost(message, repeated, tag="4")]
    pub layers: ::prost::alloc::vec::Vec<VideoLayer>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TrackInfo {
    #[prost(string, tag="1")]
    pub sid: ::prost::alloc::string::String,
    #[prost(enumeration="TrackType", tag="2")]
    pub r#type: i32,
    #[prost(string, tag="3")]
    pub name: ::prost::alloc::string::String,
    #[prost(bool, tag="4")]
    pub muted: bool,
    /// original width of video (unset for audio)
    /// clients may receive a lower resolution version with simulcast
    #[prost(uint32, tag="5")]
    pub width: u32,
    /// original height of video (unset for audio)
    #[prost(uint32, tag="6")]
    pub height: u32,
    /// true if track is simulcasted
    #[prost(bool, tag="7")]
    pub simulcast: bool,
    /// true if DTX (Discontinuous Transmission) is disabled for audio
    #[prost(bool, tag="8")]
    pub disable_dtx: bool,
    /// source of media
    #[prost(enumeration="TrackSource", tag="9")]
    pub source: i32,
    #[prost(message, repeated, tag="10")]
    pub layers: ::prost::alloc::vec::Vec<VideoLayer>,
    /// mime type of codec
    #[prost(string, tag="11")]
    pub mime_type: ::prost::alloc::string::String,
    #[prost(string, tag="12")]
    pub mid: ::prost::alloc::string::String,
    #[prost(message, repeated, tag="13")]
    pub codecs: ::prost::alloc::vec::Vec<SimulcastCodecInfo>,
    #[prost(bool, tag="14")]
    pub stereo: bool,
    /// true if RED (Redundant Encoding) is disabled for audio
    #[prost(bool, tag="15")]
    pub disable_red: bool,
    #[prost(enumeration="encryption::Type", tag="16")]
    pub encryption: i32,
    #[prost(string, tag="17")]
    pub stream: ::prost::alloc::string::String,
    #[prost(message, optional, tag="18")]
    pub version: ::core::option::Option<TimedVersion>,
    #[prost(enumeration="AudioTrackFeature", repeated, tag="19")]
    pub audio_features: ::prost::alloc::vec::Vec<i32>,
    #[prost(enumeration="BackupCodecPolicy", tag="20")]
    pub backup_codec_policy: i32,
}
/// provide information about available spatial layers
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct VideoLayer {
    /// for tracks with a single layer, this should be HIGH
    #[prost(enumeration="VideoQuality", tag="1")]
    pub quality: i32,
    #[prost(uint32, tag="2")]
    pub width: u32,
    #[prost(uint32, tag="3")]
    pub height: u32,
    /// target bitrate in bit per second (bps), server will measure actual
    #[prost(uint32, tag="4")]
    pub bitrate: u32,
    #[prost(uint32, tag="5")]
    pub ssrc: u32,
}
/// new DataPacket API
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DataPacket {
    #[deprecated]
    #[prost(enumeration="data_packet::Kind", tag="1")]
    pub kind: i32,
    /// participant identity of user that sent the message
    #[prost(string, tag="4")]
    pub participant_identity: ::prost::alloc::string::String,
    /// identities of participants who will receive the message (sent to all by default)
    #[prost(string, repeated, tag="5")]
    pub destination_identities: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// sequence number of reliable packet
    #[prost(uint32, tag="16")]
    pub sequence: u32,
    /// sid of the user that sent the message
    #[prost(string, tag="17")]
    pub participant_sid: ::prost::alloc::string::String,
    #[prost(oneof="data_packet::Value", tags="2, 3, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15")]
    pub value: ::core::option::Option<data_packet::Value>,
}
/// Nested message and enum types in `DataPacket`.
pub mod data_packet {
    #[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
    #[repr(i32)]
    pub enum Kind {
        Reliable = 0,
        Lossy = 1,
    }
    impl Kind {
        /// String value of the enum field names used in the ProtoBuf definition.
        ///
        /// The values are not transformed in any way and thus are considered stable
        /// (if the ProtoBuf definition does not change) and safe for programmatic use.
        pub fn as_str_name(&self) -> &'static str {
            match self {
                Kind::Reliable => "RELIABLE",
                Kind::Lossy => "LOSSY",
            }
        }
        /// Creates an enum from field names used in the ProtoBuf definition.
        pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
            match value {
                "RELIABLE" => Some(Self::Reliable),
                "LOSSY" => Some(Self::Lossy),
                _ => None,
            }
        }
    }
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Value {
        #[prost(message, tag="2")]
        User(super::UserPacket),
        #[prost(message, tag="3")]
        Speaker(super::ActiveSpeakerUpdate),
        #[prost(message, tag="6")]
        SipDtmf(super::SipDtmf),
        #[prost(message, tag="7")]
        Transcription(super::Transcription),
        #[prost(message, tag="8")]
        Metrics(super::MetricsBatch),
        #[prost(message, tag="9")]
        ChatMessage(super::ChatMessage),
        #[prost(message, tag="10")]
        RpcRequest(super::RpcRequest),
        #[prost(message, tag="11")]
        RpcAck(super::RpcAck),
        #[prost(message, tag="12")]
        RpcResponse(super::RpcResponse),
        #[prost(message, tag="13")]
        StreamHeader(super::data_stream::Header),
        #[prost(message, tag="14")]
        StreamChunk(super::data_stream::Chunk),
        #[prost(message, tag="15")]
        StreamTrailer(super::data_stream::Trailer),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ActiveSpeakerUpdate {
    #[prost(message, repeated, tag="1")]
    pub speakers: ::prost::alloc::vec::Vec<SpeakerInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SpeakerInfo {
    #[prost(string, tag="1")]
    pub sid: ::prost::alloc::string::String,
    /// audio level, 0-1.0, 1 is loudest
    #[prost(float, tag="2")]
    pub level: f32,
    /// true if speaker is currently active
    #[prost(bool, tag="3")]
    pub active: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UserPacket {
    /// participant ID of user that sent the message
    #[deprecated]
    #[prost(string, tag="1")]
    pub participant_sid: ::prost::alloc::string::String,
    #[deprecated]
    #[prost(string, tag="5")]
    pub participant_identity: ::prost::alloc::string::String,
    /// user defined payload
    #[prost(bytes="vec", tag="2")]
    pub payload: ::prost::alloc::vec::Vec<u8>,
    /// the ID of the participants who will receive the message (sent to all by default)
    #[deprecated]
    #[prost(string, repeated, tag="3")]
    pub destination_sids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// identities of participants who will receive the message (sent to all by default)
    #[deprecated]
    #[prost(string, repeated, tag="6")]
    pub destination_identities: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// topic under which the message was published
    #[prost(string, optional, tag="4")]
    pub topic: ::core::option::Option<::prost::alloc::string::String>,
    /// Unique ID to indentify the message
    #[prost(string, optional, tag="8")]
    pub id: ::core::option::Option<::prost::alloc::string::String>,
    /// start and end time allow relating the message to specific media time
    #[prost(uint64, optional, tag="9")]
    pub start_time: ::core::option::Option<u64>,
    #[prost(uint64, optional, tag="10")]
    pub end_time: ::core::option::Option<u64>,
    /// added by SDK to enable de-duping of messages, for INTERNAL USE ONLY
    #[prost(bytes="vec", tag="11")]
    pub nonce: ::prost::alloc::vec::Vec<u8>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipDtmf {
    #[prost(uint32, tag="3")]
    pub code: u32,
    #[prost(string, tag="4")]
    pub digit: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Transcription {
    /// Participant that got its speech transcribed
    #[prost(string, tag="2")]
    pub transcribed_participant_identity: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub track_id: ::prost::alloc::string::String,
    #[prost(message, repeated, tag="4")]
    pub segments: ::prost::alloc::vec::Vec<TranscriptionSegment>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TranscriptionSegment {
    #[prost(string, tag="1")]
    pub id: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub text: ::prost::alloc::string::String,
    #[prost(uint64, tag="3")]
    pub start_time: u64,
    #[prost(uint64, tag="4")]
    pub end_time: u64,
    #[prost(bool, tag="5")]
    pub r#final: bool,
    #[prost(string, tag="6")]
    pub language: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ChatMessage {
    /// uuid
    #[prost(string, tag="1")]
    pub id: ::prost::alloc::string::String,
    #[prost(int64, tag="2")]
    pub timestamp: i64,
    /// populated only if the intent is to edit/update an existing message
    #[prost(int64, optional, tag="3")]
    pub edit_timestamp: ::core::option::Option<i64>,
    #[prost(string, tag="4")]
    pub message: ::prost::alloc::string::String,
    /// true to remove message
    #[prost(bool, tag="5")]
    pub deleted: bool,
    /// true if the chat message has been generated by an agent from a participant's audio transcription
    #[prost(bool, tag="6")]
    pub generated: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RpcRequest {
    #[prost(string, tag="1")]
    pub id: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub method: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub payload: ::prost::alloc::string::String,
    #[prost(uint32, tag="4")]
    pub response_timeout_ms: u32,
    #[prost(uint32, tag="5")]
    pub version: u32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RpcAck {
    #[prost(string, tag="1")]
    pub request_id: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RpcResponse {
    #[prost(string, tag="1")]
    pub request_id: ::prost::alloc::string::String,
    #[prost(oneof="rpc_response::Value", tags="2, 3")]
    pub value: ::core::option::Option<rpc_response::Value>,
}
/// Nested message and enum types in `RpcResponse`.
pub mod rpc_response {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Value {
        #[prost(string, tag="2")]
        Payload(::prost::alloc::string::String),
        #[prost(message, tag="3")]
        Error(super::RpcError),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RpcError {
    #[prost(uint32, tag="1")]
    pub code: u32,
    #[prost(string, tag="2")]
    pub message: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub data: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ParticipantTracks {
    /// participant ID of participant to whom the tracks belong
    #[prost(string, tag="1")]
    pub participant_sid: ::prost::alloc::string::String,
    #[prost(string, repeated, tag="2")]
    pub track_sids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
/// details about the server
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ServerInfo {
    #[prost(enumeration="server_info::Edition", tag="1")]
    pub edition: i32,
    #[prost(string, tag="2")]
    pub version: ::prost::alloc::string::String,
    #[prost(int32, tag="3")]
    pub protocol: i32,
    #[prost(string, tag="4")]
    pub region: ::prost::alloc::string::String,
    #[prost(string, tag="5")]
    pub node_id: ::prost::alloc::string::String,
    /// additional debugging information. sent only if server is in development mode
    #[prost(string, tag="6")]
    pub debug_info: ::prost::alloc::string::String,
    #[prost(int32, tag="7")]
    pub agent_protocol: i32,
}
/// Nested message and enum types in `ServerInfo`.
pub mod server_info {
    #[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
    #[repr(i32)]
    pub enum Edition {
        Standard = 0,
        Cloud = 1,
    }
    impl Edition {
        /// String value of the enum field names used in the ProtoBuf definition.
        ///
        /// The values are not transformed in any way and thus are considered stable
        /// (if the ProtoBuf definition does not change) and safe for programmatic use.
        pub fn as_str_name(&self) -> &'static str {
            match self {
                Edition::Standard => "Standard",
                Edition::Cloud => "Cloud",
            }
        }
        /// Creates an enum from field names used in the ProtoBuf definition.
        pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
            match value {
                "Standard" => Some(Self::Standard),
                "Cloud" => Some(Self::Cloud),
                _ => None,
            }
        }
    }
}
/// details about the client
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ClientInfo {
    #[prost(enumeration="client_info::Sdk", tag="1")]
    pub sdk: i32,
    #[prost(string, tag="2")]
    pub version: ::prost::alloc::string::String,
    #[prost(int32, tag="3")]
    pub protocol: i32,
    #[prost(string, tag="4")]
    pub os: ::prost::alloc::string::String,
    #[prost(string, tag="5")]
    pub os_version: ::prost::alloc::string::String,
    #[prost(string, tag="6")]
    pub device_model: ::prost::alloc::string::String,
    #[prost(string, tag="7")]
    pub browser: ::prost::alloc::string::String,
    #[prost(string, tag="8")]
    pub browser_version: ::prost::alloc::string::String,
    #[prost(string, tag="9")]
    pub address: ::prost::alloc::string::String,
    /// wifi, wired, cellular, vpn, empty if not known
    #[prost(string, tag="10")]
    pub network: ::prost::alloc::string::String,
    /// comma separated list of additional LiveKit SDKs in use of this client, with versions
    /// e.g. "components-js:1.2.3,track-processors-js:1.2.3"
    #[prost(string, tag="11")]
    pub other_sdks: ::prost::alloc::string::String,
}
/// Nested message and enum types in `ClientInfo`.
pub mod client_info {
    #[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
    #[repr(i32)]
    pub enum Sdk {
        Unknown = 0,
        Js = 1,
        Swift = 2,
        Android = 3,
        Flutter = 4,
        Go = 5,
        Unity = 6,
        ReactNative = 7,
        Rust = 8,
        Python = 9,
        Cpp = 10,
        UnityWeb = 11,
        Node = 12,
        Unreal = 13,
    }
    impl Sdk {
        /// String value of the enum field names used in the ProtoBuf definition.
        ///
        /// The values are not transformed in any way and thus are considered stable
        /// (if the ProtoBuf definition does not change) and safe for programmatic use.
        pub fn as_str_name(&self) -> &'static str {
            match self {
                Sdk::Unknown => "UNKNOWN",
                Sdk::Js => "JS",
                Sdk::Swift => "SWIFT",
                Sdk::Android => "ANDROID",
                Sdk::Flutter => "FLUTTER",
                Sdk::Go => "GO",
                Sdk::Unity => "UNITY",
                Sdk::ReactNative => "REACT_NATIVE",
                Sdk::Rust => "RUST",
                Sdk::Python => "PYTHON",
                Sdk::Cpp => "CPP",
                Sdk::UnityWeb => "UNITY_WEB",
                Sdk::Node => "NODE",
                Sdk::Unreal => "UNREAL",
            }
        }
        /// Creates an enum from field names used in the ProtoBuf definition.
        pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
            match value {
                "UNKNOWN" => Some(Self::Unknown),
                "JS" => Some(Self::Js),
                "SWIFT" => Some(Self::Swift),
                "ANDROID" => Some(Self::Android),
                "FLUTTER" => Some(Self::Flutter),
                "GO" => Some(Self::Go),
                "UNITY" => Some(Self::Unity),
                "REACT_NATIVE" => Some(Self::ReactNative),
                "RUST" => Some(Self::Rust),
                "PYTHON" => Some(Self::Python),
                "CPP" => Some(Self::Cpp),
                "UNITY_WEB" => Some(Self::UnityWeb),
                "NODE" => Some(Self::Node),
                "UNREAL" => Some(Self::Unreal),
                _ => None,
            }
        }
    }
}
/// server provided client configuration
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ClientConfiguration {
    #[prost(message, optional, tag="1")]
    pub video: ::core::option::Option<VideoConfiguration>,
    #[prost(message, optional, tag="2")]
    pub screen: ::core::option::Option<VideoConfiguration>,
    #[prost(enumeration="ClientConfigSetting", tag="3")]
    pub resume_connection: i32,
    #[prost(message, optional, tag="4")]
    pub disabled_codecs: ::core::option::Option<DisabledCodecs>,
    #[prost(enumeration="ClientConfigSetting", tag="5")]
    pub force_relay: i32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct VideoConfiguration {
    #[prost(enumeration="ClientConfigSetting", tag="1")]
    pub hardware_encoder: i32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DisabledCodecs {
    /// disabled for both publish and subscribe
    #[prost(message, repeated, tag="1")]
    pub codecs: ::prost::alloc::vec::Vec<Codec>,
    /// only disable for publish
    #[prost(message, repeated, tag="2")]
    pub publish: ::prost::alloc::vec::Vec<Codec>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RtpDrift {
    #[prost(message, optional, tag="1")]
    pub start_time: ::core::option::Option<::pbjson_types::Timestamp>,
    #[prost(message, optional, tag="2")]
    pub end_time: ::core::option::Option<::pbjson_types::Timestamp>,
    #[prost(double, tag="3")]
    pub duration: f64,
    #[prost(uint64, tag="4")]
    pub start_timestamp: u64,
    #[prost(uint64, tag="5")]
    pub end_timestamp: u64,
    #[prost(uint64, tag="6")]
    pub rtp_clock_ticks: u64,
    #[prost(int64, tag="7")]
    pub drift_samples: i64,
    #[prost(double, tag="8")]
    pub drift_ms: f64,
    #[prost(double, tag="9")]
    pub clock_rate: f64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RtpStats {
    #[prost(message, optional, tag="1")]
    pub start_time: ::core::option::Option<::pbjson_types::Timestamp>,
    #[prost(message, optional, tag="2")]
    pub end_time: ::core::option::Option<::pbjson_types::Timestamp>,
    #[prost(double, tag="3")]
    pub duration: f64,
    #[prost(uint32, tag="4")]
    pub packets: u32,
    #[prost(double, tag="5")]
    pub packet_rate: f64,
    #[prost(uint64, tag="6")]
    pub bytes: u64,
    #[prost(uint64, tag="39")]
    pub header_bytes: u64,
    #[prost(double, tag="7")]
    pub bitrate: f64,
    #[prost(uint32, tag="8")]
    pub packets_lost: u32,
    #[prost(double, tag="9")]
    pub packet_loss_rate: f64,
    #[prost(float, tag="10")]
    pub packet_loss_percentage: f32,
    #[prost(uint32, tag="11")]
    pub packets_duplicate: u32,
    #[prost(double, tag="12")]
    pub packet_duplicate_rate: f64,
    #[prost(uint64, tag="13")]
    pub bytes_duplicate: u64,
    #[prost(uint64, tag="40")]
    pub header_bytes_duplicate: u64,
    #[prost(double, tag="14")]
    pub bitrate_duplicate: f64,
    #[prost(uint32, tag="15")]
    pub packets_padding: u32,
    #[prost(double, tag="16")]
    pub packet_padding_rate: f64,
    #[prost(uint64, tag="17")]
    pub bytes_padding: u64,
    #[prost(uint64, tag="41")]
    pub header_bytes_padding: u64,
    #[prost(double, tag="18")]
    pub bitrate_padding: f64,
    #[prost(uint32, tag="19")]
    pub packets_out_of_order: u32,
    #[prost(uint32, tag="20")]
    pub frames: u32,
    #[prost(double, tag="21")]
    pub frame_rate: f64,
    #[prost(double, tag="22")]
    pub jitter_current: f64,
    #[prost(double, tag="23")]
    pub jitter_max: f64,
    #[prost(map="int32, uint32", tag="24")]
    pub gap_histogram: ::std::collections::HashMap<i32, u32>,
    #[prost(uint32, tag="25")]
    pub nacks: u32,
    #[prost(uint32, tag="37")]
    pub nack_acks: u32,
    #[prost(uint32, tag="26")]
    pub nack_misses: u32,
    #[prost(uint32, tag="38")]
    pub nack_repeated: u32,
    #[prost(uint32, tag="27")]
    pub plis: u32,
    #[prost(message, optional, tag="28")]
    pub last_pli: ::core::option::Option<::pbjson_types::Timestamp>,
    #[prost(uint32, tag="29")]
    pub firs: u32,
    #[prost(message, optional, tag="30")]
    pub last_fir: ::core::option::Option<::pbjson_types::Timestamp>,
    #[prost(uint32, tag="31")]
    pub rtt_current: u32,
    #[prost(uint32, tag="32")]
    pub rtt_max: u32,
    #[prost(uint32, tag="33")]
    pub key_frames: u32,
    #[prost(message, optional, tag="34")]
    pub last_key_frame: ::core::option::Option<::pbjson_types::Timestamp>,
    #[prost(uint32, tag="35")]
    pub layer_lock_plis: u32,
    #[prost(message, optional, tag="36")]
    pub last_layer_lock_pli: ::core::option::Option<::pbjson_types::Timestamp>,
    #[prost(message, optional, tag="44")]
    pub packet_drift: ::core::option::Option<RtpDrift>,
    #[prost(message, optional, tag="45")]
    pub ntp_report_drift: ::core::option::Option<RtpDrift>,
    #[prost(message, optional, tag="46")]
    pub rebased_report_drift: ::core::option::Option<RtpDrift>,
    /// NEXT_ID: 48
    #[prost(message, optional, tag="47")]
    pub received_report_drift: ::core::option::Option<RtpDrift>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RtcpSenderReportState {
    #[prost(uint32, tag="1")]
    pub rtp_timestamp: u32,
    #[prost(uint64, tag="2")]
    pub rtp_timestamp_ext: u64,
    #[prost(uint64, tag="3")]
    pub ntp_timestamp: u64,
    /// time at which this happened
    #[prost(int64, tag="4")]
    pub at: i64,
    #[prost(int64, tag="5")]
    pub at_adjusted: i64,
    #[prost(uint32, tag="6")]
    pub packets: u32,
    #[prost(uint64, tag="7")]
    pub octets: u64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RtpForwarderState {
    #[prost(bool, tag="1")]
    pub started: bool,
    #[prost(int32, tag="2")]
    pub reference_layer_spatial: i32,
    #[prost(int64, tag="3")]
    pub pre_start_time: i64,
    #[prost(uint64, tag="4")]
    pub ext_first_timestamp: u64,
    #[prost(uint64, tag="5")]
    pub dummy_start_timestamp_offset: u64,
    #[prost(message, optional, tag="6")]
    pub rtp_munger: ::core::option::Option<RtpMungerState>,
    #[prost(message, repeated, tag="8")]
    pub sender_report_state: ::prost::alloc::vec::Vec<RtcpSenderReportState>,
    #[prost(oneof="rtp_forwarder_state::CodecMunger", tags="7")]
    pub codec_munger: ::core::option::Option<rtp_forwarder_state::CodecMunger>,
}
/// Nested message and enum types in `RTPForwarderState`.
pub mod rtp_forwarder_state {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum CodecMunger {
        #[prost(message, tag="7")]
        Vp8Munger(super::Vp8MungerState),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RtpMungerState {
    #[prost(uint64, tag="1")]
    pub ext_last_sequence_number: u64,
    #[prost(uint64, tag="2")]
    pub ext_second_last_sequence_number: u64,
    #[prost(uint64, tag="3")]
    pub ext_last_timestamp: u64,
    #[prost(uint64, tag="4")]
    pub ext_second_last_timestamp: u64,
    #[prost(bool, tag="5")]
    pub last_marker: bool,
    #[prost(bool, tag="6")]
    pub second_last_marker: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Vp8MungerState {
    #[prost(int32, tag="1")]
    pub ext_last_picture_id: i32,
    #[prost(bool, tag="2")]
    pub picture_id_used: bool,
    #[prost(uint32, tag="3")]
    pub last_tl0_pic_idx: u32,
    #[prost(bool, tag="4")]
    pub tl0_pic_idx_used: bool,
    #[prost(bool, tag="5")]
    pub tid_used: bool,
    #[prost(uint32, tag="6")]
    pub last_key_idx: u32,
    #[prost(bool, tag="7")]
    pub key_idx_used: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TimedVersion {
    #[prost(int64, tag="1")]
    pub unix_micro: i64,
    #[prost(int32, tag="2")]
    pub ticks: i32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DataStream {
}
/// Nested message and enum types in `DataStream`.
pub mod data_stream {
    /// header properties specific to text streams
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
    pub struct TextHeader {
        #[prost(enumeration="OperationType", tag="1")]
        pub operation_type: i32,
        /// Optional: Version for updates/edits
        #[prost(int32, tag="2")]
        pub version: i32,
        /// Optional: Reply to specific message
        #[prost(string, tag="3")]
        pub reply_to_stream_id: ::prost::alloc::string::String,
        /// file attachments for text streams
        #[prost(string, repeated, tag="4")]
        pub attached_stream_ids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
        /// true if the text has been generated by an agent from a participant's audio transcription
        #[prost(bool, tag="5")]
        pub generated: bool,
    }
    /// header properties specific to byte or file streams
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
    pub struct ByteHeader {
        #[prost(string, tag="1")]
        pub name: ::prost::alloc::string::String,
    }
    /// main DataStream.Header that contains a oneof for specific headers
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
    pub struct Header {
        /// unique identifier for this data stream
        #[prost(string, tag="1")]
        pub stream_id: ::prost::alloc::string::String,
        /// using int64 for Unix timestamp
        #[prost(int64, tag="2")]
        pub timestamp: i64,
        #[prost(string, tag="3")]
        pub topic: ::prost::alloc::string::String,
        #[prost(string, tag="4")]
        pub mime_type: ::prost::alloc::string::String,
        /// only populated for finite streams, if it's a stream of unknown size this stays empty
        #[prost(uint64, optional, tag="5")]
        pub total_length: ::core::option::Option<u64>,
        /// defaults to NONE
        #[prost(enumeration="super::encryption::Type", tag="7")]
        pub encryption_type: i32,
        /// user defined attributes map that can carry additional info
        #[prost(map="string, string", tag="8")]
        pub attributes: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
        /// oneof to choose between specific header types
        #[prost(oneof="header::ContentHeader", tags="9, 10")]
        pub content_header: ::core::option::Option<header::ContentHeader>,
    }
    /// Nested message and enum types in `Header`.
    pub mod header {
        /// oneof to choose between specific header types
        #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
        pub enum ContentHeader {
            #[prost(message, tag="9")]
            TextHeader(super::TextHeader),
            #[prost(message, tag="10")]
            ByteHeader(super::ByteHeader),
        }
    }
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
    pub struct Chunk {
        /// unique identifier for this data stream to map it to the correct header
        #[prost(string, tag="1")]
        pub stream_id: ::prost::alloc::string::String,
        #[prost(uint64, tag="2")]
        pub chunk_index: u64,
        /// content as binary (bytes)
        #[prost(bytes="vec", tag="3")]
        pub content: ::prost::alloc::vec::Vec<u8>,
        /// a version indicating that this chunk_index has been retroactively modified and the original one needs to be replaced
        #[prost(int32, tag="4")]
        pub version: i32,
        /// optional, initialization vector for AES-GCM encryption
        #[prost(bytes="vec", optional, tag="5")]
        pub iv: ::core::option::Option<::prost::alloc::vec::Vec<u8>>,
    }
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
    pub struct Trailer {
        /// unique identifier for this data stream
        #[prost(string, tag="1")]
        pub stream_id: ::prost::alloc::string::String,
        /// reason why the stream was closed (could contain "error" / "interrupted" / empty for expected end)
        #[prost(string, tag="2")]
        pub reason: ::prost::alloc::string::String,
        /// finalizing updates for the stream, can also include additional insights for errors or endTime for transcription
        #[prost(map="string, string", tag="3")]
        pub attributes: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    }
    /// enum for operation types (specific to TextHeader)
    #[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
    #[repr(i32)]
    pub enum OperationType {
        Create = 0,
        Update = 1,
        Delete = 2,
        Reaction = 3,
    }
    impl OperationType {
        /// String value of the enum field names used in the ProtoBuf definition.
        ///
        /// The values are not transformed in any way and thus are considered stable
        /// (if the ProtoBuf definition does not change) and safe for programmatic use.
        pub fn as_str_name(&self) -> &'static str {
            match self {
                OperationType::Create => "CREATE",
                OperationType::Update => "UPDATE",
                OperationType::Delete => "DELETE",
                OperationType::Reaction => "REACTION",
            }
        }
        /// Creates an enum from field names used in the ProtoBuf definition.
        pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
            match value {
                "CREATE" => Some(Self::Create),
                "UPDATE" => Some(Self::Update),
                "DELETE" => Some(Self::Delete),
                "REACTION" => Some(Self::Reaction),
                _ => None,
            }
        }
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct WebhookConfig {
    #[prost(string, tag="1")]
    pub url: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub signing_key: ::prost::alloc::string::String,
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum AudioCodec {
    DefaultAc = 0,
    Opus = 1,
    Aac = 2,
}
impl AudioCodec {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            AudioCodec::DefaultAc => "DEFAULT_AC",
            AudioCodec::Opus => "OPUS",
            AudioCodec::Aac => "AAC",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_AC" => Some(Self::DefaultAc),
            "OPUS" => Some(Self::Opus),
            "AAC" => Some(Self::Aac),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum VideoCodec {
    DefaultVc = 0,
    H264Baseline = 1,
    H264Main = 2,
    H264High = 3,
    Vp8 = 4,
}
impl VideoCodec {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            VideoCodec::DefaultVc => "DEFAULT_VC",
            VideoCodec::H264Baseline => "H264_BASELINE",
            VideoCodec::H264Main => "H264_MAIN",
            VideoCodec::H264High => "H264_HIGH",
            VideoCodec::Vp8 => "VP8",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_VC" => Some(Self::DefaultVc),
            "H264_BASELINE" => Some(Self::H264Baseline),
            "H264_MAIN" => Some(Self::H264Main),
            "H264_HIGH" => Some(Self::H264High),
            "VP8" => Some(Self::Vp8),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum ImageCodec {
    IcDefault = 0,
    IcJpeg = 1,
}
impl ImageCodec {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            ImageCodec::IcDefault => "IC_DEFAULT",
            ImageCodec::IcJpeg => "IC_JPEG",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "IC_DEFAULT" => Some(Self::IcDefault),
            "IC_JPEG" => Some(Self::IcJpeg),
            _ => None,
        }
    }
}
/// Policy for publisher to handle subscribers that are unable to support the primary codec of a track
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum BackupCodecPolicy {
    /// default behavior, the track prefer to regress to backup codec and all subscribers will receive the backup codec,
    /// the sfu will try to regress codec if possible but not assured.
    PreferRegression = 0,
    /// encoding/send the primary and backup codec simultaneously
    Simulcast = 1,
    /// force the track to regress to backup codec, this option can be used in video conference or the publisher has limited bandwidth/encoding power
    Regression = 2,
}
impl BackupCodecPolicy {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            BackupCodecPolicy::PreferRegression => "PREFER_REGRESSION",
            BackupCodecPolicy::Simulcast => "SIMULCAST",
            BackupCodecPolicy::Regression => "REGRESSION",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "PREFER_REGRESSION" => Some(Self::PreferRegression),
            "SIMULCAST" => Some(Self::Simulcast),
            "REGRESSION" => Some(Self::Regression),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum TrackType {
    Audio = 0,
    Video = 1,
    Data = 2,
}
impl TrackType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            TrackType::Audio => "AUDIO",
            TrackType::Video => "VIDEO",
            TrackType::Data => "DATA",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "AUDIO" => Some(Self::Audio),
            "VIDEO" => Some(Self::Video),
            "DATA" => Some(Self::Data),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum TrackSource {
    Unknown = 0,
    Camera = 1,
    Microphone = 2,
    ScreenShare = 3,
    ScreenShareAudio = 4,
}
impl TrackSource {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            TrackSource::Unknown => "UNKNOWN",
            TrackSource::Camera => "CAMERA",
            TrackSource::Microphone => "MICROPHONE",
            TrackSource::ScreenShare => "SCREEN_SHARE",
            TrackSource::ScreenShareAudio => "SCREEN_SHARE_AUDIO",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "UNKNOWN" => Some(Self::Unknown),
            "CAMERA" => Some(Self::Camera),
            "MICROPHONE" => Some(Self::Microphone),
            "SCREEN_SHARE" => Some(Self::ScreenShare),
            "SCREEN_SHARE_AUDIO" => Some(Self::ScreenShareAudio),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum VideoQuality {
    Low = 0,
    Medium = 1,
    High = 2,
    Off = 3,
}
impl VideoQuality {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            VideoQuality::Low => "LOW",
            VideoQuality::Medium => "MEDIUM",
            VideoQuality::High => "HIGH",
            VideoQuality::Off => "OFF",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "LOW" => Some(Self::Low),
            "MEDIUM" => Some(Self::Medium),
            "HIGH" => Some(Self::High),
            "OFF" => Some(Self::Off),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum ConnectionQuality {
    Poor = 0,
    Good = 1,
    Excellent = 2,
    Lost = 3,
}
impl ConnectionQuality {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            ConnectionQuality::Poor => "POOR",
            ConnectionQuality::Good => "GOOD",
            ConnectionQuality::Excellent => "EXCELLENT",
            ConnectionQuality::Lost => "LOST",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "POOR" => Some(Self::Poor),
            "GOOD" => Some(Self::Good),
            "EXCELLENT" => Some(Self::Excellent),
            "LOST" => Some(Self::Lost),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum ClientConfigSetting {
    Unset = 0,
    Disabled = 1,
    Enabled = 2,
}
impl ClientConfigSetting {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            ClientConfigSetting::Unset => "UNSET",
            ClientConfigSetting::Disabled => "DISABLED",
            ClientConfigSetting::Enabled => "ENABLED",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "UNSET" => Some(Self::Unset),
            "DISABLED" => Some(Self::Disabled),
            "ENABLED" => Some(Self::Enabled),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum DisconnectReason {
    UnknownReason = 0,
    /// the client initiated the disconnect
    ClientInitiated = 1,
    /// another participant with the same identity has joined the room
    DuplicateIdentity = 2,
    /// the server instance is shutting down
    ServerShutdown = 3,
    /// RoomService.RemoveParticipant was called
    ParticipantRemoved = 4,
    /// RoomService.DeleteRoom was called
    RoomDeleted = 5,
    /// the client is attempting to resume a session, but server is not aware of it
    StateMismatch = 6,
    /// client was unable to connect fully
    JoinFailure = 7,
    /// Cloud-only, the server requested Participant to migrate the connection elsewhere
    Migration = 8,
    /// the signal websocket was closed unexpectedly
    SignalClose = 9,
    /// the room was closed, due to all Standard and Ingress participants having left
    RoomClosed = 10,
    /// SIP callee did not respond in time
    UserUnavailable = 11,
    /// SIP callee rejected the call (busy)
    UserRejected = 12,
    /// SIP protocol failure or unexpected response
    SipTrunkFailure = 13,
    /// server timed out a participant session
    ConnectionTimeout = 14,
    /// media stream failure or media timeout
    MediaFailure = 15,
}
impl DisconnectReason {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            DisconnectReason::UnknownReason => "UNKNOWN_REASON",
            DisconnectReason::ClientInitiated => "CLIENT_INITIATED",
            DisconnectReason::DuplicateIdentity => "DUPLICATE_IDENTITY",
            DisconnectReason::ServerShutdown => "SERVER_SHUTDOWN",
            DisconnectReason::ParticipantRemoved => "PARTICIPANT_REMOVED",
            DisconnectReason::RoomDeleted => "ROOM_DELETED",
            DisconnectReason::StateMismatch => "STATE_MISMATCH",
            DisconnectReason::JoinFailure => "JOIN_FAILURE",
            DisconnectReason::Migration => "MIGRATION",
            DisconnectReason::SignalClose => "SIGNAL_CLOSE",
            DisconnectReason::RoomClosed => "ROOM_CLOSED",
            DisconnectReason::UserUnavailable => "USER_UNAVAILABLE",
            DisconnectReason::UserRejected => "USER_REJECTED",
            DisconnectReason::SipTrunkFailure => "SIP_TRUNK_FAILURE",
            DisconnectReason::ConnectionTimeout => "CONNECTION_TIMEOUT",
            DisconnectReason::MediaFailure => "MEDIA_FAILURE",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "UNKNOWN_REASON" => Some(Self::UnknownReason),
            "CLIENT_INITIATED" => Some(Self::ClientInitiated),
            "DUPLICATE_IDENTITY" => Some(Self::DuplicateIdentity),
            "SERVER_SHUTDOWN" => Some(Self::ServerShutdown),
            "PARTICIPANT_REMOVED" => Some(Self::ParticipantRemoved),
            "ROOM_DELETED" => Some(Self::RoomDeleted),
            "STATE_MISMATCH" => Some(Self::StateMismatch),
            "JOIN_FAILURE" => Some(Self::JoinFailure),
            "MIGRATION" => Some(Self::Migration),
            "SIGNAL_CLOSE" => Some(Self::SignalClose),
            "ROOM_CLOSED" => Some(Self::RoomClosed),
            "USER_UNAVAILABLE" => Some(Self::UserUnavailable),
            "USER_REJECTED" => Some(Self::UserRejected),
            "SIP_TRUNK_FAILURE" => Some(Self::SipTrunkFailure),
            "CONNECTION_TIMEOUT" => Some(Self::ConnectionTimeout),
            "MEDIA_FAILURE" => Some(Self::MediaFailure),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum ReconnectReason {
    RrUnknown = 0,
    RrSignalDisconnected = 1,
    RrPublisherFailed = 2,
    RrSubscriberFailed = 3,
    RrSwitchCandidate = 4,
}
impl ReconnectReason {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            ReconnectReason::RrUnknown => "RR_UNKNOWN",
            ReconnectReason::RrSignalDisconnected => "RR_SIGNAL_DISCONNECTED",
            ReconnectReason::RrPublisherFailed => "RR_PUBLISHER_FAILED",
            ReconnectReason::RrSubscriberFailed => "RR_SUBSCRIBER_FAILED",
            ReconnectReason::RrSwitchCandidate => "RR_SWITCH_CANDIDATE",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "RR_UNKNOWN" => Some(Self::RrUnknown),
            "RR_SIGNAL_DISCONNECTED" => Some(Self::RrSignalDisconnected),
            "RR_PUBLISHER_FAILED" => Some(Self::RrPublisherFailed),
            "RR_SUBSCRIBER_FAILED" => Some(Self::RrSubscriberFailed),
            "RR_SWITCH_CANDIDATE" => Some(Self::RrSwitchCandidate),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum SubscriptionError {
    SeUnknown = 0,
    SeCodecUnsupported = 1,
    SeTrackNotfound = 2,
}
impl SubscriptionError {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            SubscriptionError::SeUnknown => "SE_UNKNOWN",
            SubscriptionError::SeCodecUnsupported => "SE_CODEC_UNSUPPORTED",
            SubscriptionError::SeTrackNotfound => "SE_TRACK_NOTFOUND",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "SE_UNKNOWN" => Some(Self::SeUnknown),
            "SE_CODEC_UNSUPPORTED" => Some(Self::SeCodecUnsupported),
            "SE_TRACK_NOTFOUND" => Some(Self::SeTrackNotfound),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum AudioTrackFeature {
    TfStereo = 0,
    TfNoDtx = 1,
    TfAutoGainControl = 2,
    TfEchoCancellation = 3,
    TfNoiseSuppression = 4,
    TfEnhancedNoiseCancellation = 5,
    /// client will buffer audio once available and send it to the server via bytes stream once connected
    TfPreconnectBuffer = 6,
}
impl AudioTrackFeature {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            AudioTrackFeature::TfStereo => "TF_STEREO",
            AudioTrackFeature::TfNoDtx => "TF_NO_DTX",
            AudioTrackFeature::TfAutoGainControl => "TF_AUTO_GAIN_CONTROL",
            AudioTrackFeature::TfEchoCancellation => "TF_ECHO_CANCELLATION",
            AudioTrackFeature::TfNoiseSuppression => "TF_NOISE_SUPPRESSION",
            AudioTrackFeature::TfEnhancedNoiseCancellation => "TF_ENHANCED_NOISE_CANCELLATION",
            AudioTrackFeature::TfPreconnectBuffer => "TF_PRECONNECT_BUFFER",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "TF_STEREO" => Some(Self::TfStereo),
            "TF_NO_DTX" => Some(Self::TfNoDtx),
            "TF_AUTO_GAIN_CONTROL" => Some(Self::TfAutoGainControl),
            "TF_ECHO_CANCELLATION" => Some(Self::TfEchoCancellation),
            "TF_NOISE_SUPPRESSION" => Some(Self::TfNoiseSuppression),
            "TF_ENHANCED_NOISE_CANCELLATION" => Some(Self::TfEnhancedNoiseCancellation),
            "TF_PRECONNECT_BUFFER" => Some(Self::TfPreconnectBuffer),
            _ => None,
        }
    }
}
/// composite using a web browser
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RoomCompositeEgressRequest {
    /// required
    #[prost(string, tag="1")]
    pub room_name: ::prost::alloc::string::String,
    /// (optional)
    #[prost(string, tag="2")]
    pub layout: ::prost::alloc::string::String,
    /// (default false)
    #[prost(bool, tag="3")]
    pub audio_only: bool,
    /// only applies to audio_only egress (default DEFAULT_MIXING)
    #[prost(enumeration="AudioMixing", tag="15")]
    pub audio_mixing: i32,
    /// (default false)
    #[prost(bool, tag="4")]
    pub video_only: bool,
    /// template base url (default <https://recorder.livekit.io>)
    #[prost(string, tag="5")]
    pub custom_base_url: ::prost::alloc::string::String,
    #[prost(message, repeated, tag="11")]
    pub file_outputs: ::prost::alloc::vec::Vec<EncodedFileOutput>,
    #[prost(message, repeated, tag="12")]
    pub stream_outputs: ::prost::alloc::vec::Vec<StreamOutput>,
    #[prost(message, repeated, tag="13")]
    pub segment_outputs: ::prost::alloc::vec::Vec<SegmentedFileOutput>,
    #[prost(message, repeated, tag="14")]
    pub image_outputs: ::prost::alloc::vec::Vec<ImageOutput>,
    /// extra webhooks to call for this request
    #[prost(message, repeated, tag="16")]
    pub webhooks: ::prost::alloc::vec::Vec<WebhookConfig>,
    /// deprecated (use _output fields)
    #[prost(oneof="room_composite_egress_request::Output", tags="6, 7, 10")]
    pub output: ::core::option::Option<room_composite_egress_request::Output>,
    #[prost(oneof="room_composite_egress_request::Options", tags="8, 9")]
    pub options: ::core::option::Option<room_composite_egress_request::Options>,
}
/// Nested message and enum types in `RoomCompositeEgressRequest`.
pub mod room_composite_egress_request {
    /// deprecated (use _output fields)
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Output {
        #[prost(message, tag="6")]
        File(super::EncodedFileOutput),
        #[prost(message, tag="7")]
        Stream(super::StreamOutput),
        #[prost(message, tag="10")]
        Segments(super::SegmentedFileOutput),
    }
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Options {
        /// (default H264_720P_30)
        #[prost(enumeration="super::EncodingOptionsPreset", tag="8")]
        Preset(i32),
        /// (optional)
        #[prost(message, tag="9")]
        Advanced(super::EncodingOptions),
    }
}
/// record any website
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct WebEgressRequest {
    #[prost(string, tag="1")]
    pub url: ::prost::alloc::string::String,
    #[prost(bool, tag="2")]
    pub audio_only: bool,
    #[prost(bool, tag="3")]
    pub video_only: bool,
    #[prost(bool, tag="12")]
    pub await_start_signal: bool,
    #[prost(message, repeated, tag="9")]
    pub file_outputs: ::prost::alloc::vec::Vec<EncodedFileOutput>,
    #[prost(message, repeated, tag="10")]
    pub stream_outputs: ::prost::alloc::vec::Vec<StreamOutput>,
    #[prost(message, repeated, tag="11")]
    pub segment_outputs: ::prost::alloc::vec::Vec<SegmentedFileOutput>,
    #[prost(message, repeated, tag="13")]
    pub image_outputs: ::prost::alloc::vec::Vec<ImageOutput>,
    /// extra webhooks to call for this request
    #[prost(message, repeated, tag="14")]
    pub webhooks: ::prost::alloc::vec::Vec<WebhookConfig>,
    /// deprecated (use _output fields)
    #[prost(oneof="web_egress_request::Output", tags="4, 5, 6")]
    pub output: ::core::option::Option<web_egress_request::Output>,
    #[prost(oneof="web_egress_request::Options", tags="7, 8")]
    pub options: ::core::option::Option<web_egress_request::Options>,
}
/// Nested message and enum types in `WebEgressRequest`.
pub mod web_egress_request {
    /// deprecated (use _output fields)
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Output {
        #[prost(message, tag="4")]
        File(super::EncodedFileOutput),
        #[prost(message, tag="5")]
        Stream(super::StreamOutput),
        #[prost(message, tag="6")]
        Segments(super::SegmentedFileOutput),
    }
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Options {
        #[prost(enumeration="super::EncodingOptionsPreset", tag="7")]
        Preset(i32),
        #[prost(message, tag="8")]
        Advanced(super::EncodingOptions),
    }
}
/// record audio and video from a single participant
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ParticipantEgressRequest {
    /// required
    #[prost(string, tag="1")]
    pub room_name: ::prost::alloc::string::String,
    /// required
    #[prost(string, tag="2")]
    pub identity: ::prost::alloc::string::String,
    /// (default false)
    #[prost(bool, tag="3")]
    pub screen_share: bool,
    #[prost(message, repeated, tag="6")]
    pub file_outputs: ::prost::alloc::vec::Vec<EncodedFileOutput>,
    #[prost(message, repeated, tag="7")]
    pub stream_outputs: ::prost::alloc::vec::Vec<StreamOutput>,
    #[prost(message, repeated, tag="8")]
    pub segment_outputs: ::prost::alloc::vec::Vec<SegmentedFileOutput>,
    #[prost(message, repeated, tag="9")]
    pub image_outputs: ::prost::alloc::vec::Vec<ImageOutput>,
    /// extra webhooks to call for this request
    #[prost(message, repeated, tag="10")]
    pub webhooks: ::prost::alloc::vec::Vec<WebhookConfig>,
    #[prost(oneof="participant_egress_request::Options", tags="4, 5")]
    pub options: ::core::option::Option<participant_egress_request::Options>,
}
/// Nested message and enum types in `ParticipantEgressRequest`.
pub mod participant_egress_request {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Options {
        /// (default H264_720P_30)
        #[prost(enumeration="super::EncodingOptionsPreset", tag="4")]
        Preset(i32),
        /// (optional)
        #[prost(message, tag="5")]
        Advanced(super::EncodingOptions),
    }
}
/// containerize up to one audio and one video track
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TrackCompositeEgressRequest {
    /// required
    #[prost(string, tag="1")]
    pub room_name: ::prost::alloc::string::String,
    /// (optional)
    #[prost(string, tag="2")]
    pub audio_track_id: ::prost::alloc::string::String,
    /// (optional)
    #[prost(string, tag="3")]
    pub video_track_id: ::prost::alloc::string::String,
    #[prost(message, repeated, tag="11")]
    pub file_outputs: ::prost::alloc::vec::Vec<EncodedFileOutput>,
    #[prost(message, repeated, tag="12")]
    pub stream_outputs: ::prost::alloc::vec::Vec<StreamOutput>,
    #[prost(message, repeated, tag="13")]
    pub segment_outputs: ::prost::alloc::vec::Vec<SegmentedFileOutput>,
    #[prost(message, repeated, tag="14")]
    pub image_outputs: ::prost::alloc::vec::Vec<ImageOutput>,
    /// extra webhooks to call for this request
    #[prost(message, repeated, tag="15")]
    pub webhooks: ::prost::alloc::vec::Vec<WebhookConfig>,
    /// deprecated (use _output fields)
    #[prost(oneof="track_composite_egress_request::Output", tags="4, 5, 8")]
    pub output: ::core::option::Option<track_composite_egress_request::Output>,
    #[prost(oneof="track_composite_egress_request::Options", tags="6, 7")]
    pub options: ::core::option::Option<track_composite_egress_request::Options>,
}
/// Nested message and enum types in `TrackCompositeEgressRequest`.
pub mod track_composite_egress_request {
    /// deprecated (use _output fields)
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Output {
        #[prost(message, tag="4")]
        File(super::EncodedFileOutput),
        #[prost(message, tag="5")]
        Stream(super::StreamOutput),
        #[prost(message, tag="8")]
        Segments(super::SegmentedFileOutput),
    }
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Options {
        /// (default H264_720P_30)
        #[prost(enumeration="super::EncodingOptionsPreset", tag="6")]
        Preset(i32),
        /// (optional)
        #[prost(message, tag="7")]
        Advanced(super::EncodingOptions),
    }
}
/// record tracks individually, without transcoding
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TrackEgressRequest {
    /// required
    #[prost(string, tag="1")]
    pub room_name: ::prost::alloc::string::String,
    /// required
    #[prost(string, tag="2")]
    pub track_id: ::prost::alloc::string::String,
    /// extra webhooks to call for this request
    #[prost(message, repeated, tag="5")]
    pub webhooks: ::prost::alloc::vec::Vec<WebhookConfig>,
    /// required
    #[prost(oneof="track_egress_request::Output", tags="3, 4")]
    pub output: ::core::option::Option<track_egress_request::Output>,
}
/// Nested message and enum types in `TrackEgressRequest`.
pub mod track_egress_request {
    /// required
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Output {
        #[prost(message, tag="3")]
        File(super::DirectFileOutput),
        #[prost(string, tag="4")]
        WebsocketUrl(::prost::alloc::string::String),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct EncodedFileOutput {
    /// (optional)
    #[prost(enumeration="EncodedFileType", tag="1")]
    pub file_type: i32,
    /// see egress docs for templating (default {room_name}-{time})
    #[prost(string, tag="2")]
    pub filepath: ::prost::alloc::string::String,
    /// disable upload of manifest file (default false)
    #[prost(bool, tag="6")]
    pub disable_manifest: bool,
    #[prost(oneof="encoded_file_output::Output", tags="3, 4, 5, 7")]
    pub output: ::core::option::Option<encoded_file_output::Output>,
}
/// Nested message and enum types in `EncodedFileOutput`.
pub mod encoded_file_output {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Output {
        #[prost(message, tag="3")]
        S3(super::S3Upload),
        #[prost(message, tag="4")]
        Gcp(super::GcpUpload),
        #[prost(message, tag="5")]
        Azure(super::AzureBlobUpload),
        #[prost(message, tag="7")]
        AliOss(super::AliOssUpload),
    }
}
/// Used to generate HLS segments or other kind of segmented output
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SegmentedFileOutput {
    /// (optional)
    #[prost(enumeration="SegmentedFileProtocol", tag="1")]
    pub protocol: i32,
    /// (optional)
    #[prost(string, tag="2")]
    pub filename_prefix: ::prost::alloc::string::String,
    /// (optional)
    #[prost(string, tag="3")]
    pub playlist_name: ::prost::alloc::string::String,
    /// (optional, disabled if not provided). Path of a live playlist
    #[prost(string, tag="11")]
    pub live_playlist_name: ::prost::alloc::string::String,
    /// in seconds (optional)
    #[prost(uint32, tag="4")]
    pub segment_duration: u32,
    /// (optional, default INDEX)
    #[prost(enumeration="SegmentedFileSuffix", tag="10")]
    pub filename_suffix: i32,
    /// disable upload of manifest file (default false)
    #[prost(bool, tag="8")]
    pub disable_manifest: bool,
    /// required
    #[prost(oneof="segmented_file_output::Output", tags="5, 6, 7, 9")]
    pub output: ::core::option::Option<segmented_file_output::Output>,
}
/// Nested message and enum types in `SegmentedFileOutput`.
pub mod segmented_file_output {
    /// required
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Output {
        #[prost(message, tag="5")]
        S3(super::S3Upload),
        #[prost(message, tag="6")]
        Gcp(super::GcpUpload),
        #[prost(message, tag="7")]
        Azure(super::AzureBlobUpload),
        #[prost(message, tag="9")]
        AliOss(super::AliOssUpload),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DirectFileOutput {
    /// see egress docs for templating (default {track_id}-{time})
    #[prost(string, tag="1")]
    pub filepath: ::prost::alloc::string::String,
    /// disable upload of manifest file (default false)
    #[prost(bool, tag="5")]
    pub disable_manifest: bool,
    #[prost(oneof="direct_file_output::Output", tags="2, 3, 4, 6")]
    pub output: ::core::option::Option<direct_file_output::Output>,
}
/// Nested message and enum types in `DirectFileOutput`.
pub mod direct_file_output {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Output {
        #[prost(message, tag="2")]
        S3(super::S3Upload),
        #[prost(message, tag="3")]
        Gcp(super::GcpUpload),
        #[prost(message, tag="4")]
        Azure(super::AzureBlobUpload),
        #[prost(message, tag="6")]
        AliOss(super::AliOssUpload),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ImageOutput {
    /// in seconds (required)
    #[prost(uint32, tag="1")]
    pub capture_interval: u32,
    /// (optional, defaults to track width)
    #[prost(int32, tag="2")]
    pub width: i32,
    /// (optional, defaults to track height)
    #[prost(int32, tag="3")]
    pub height: i32,
    /// (optional)
    #[prost(string, tag="4")]
    pub filename_prefix: ::prost::alloc::string::String,
    /// (optional, default INDEX)
    #[prost(enumeration="ImageFileSuffix", tag="5")]
    pub filename_suffix: i32,
    /// (optional)
    #[prost(enumeration="ImageCodec", tag="6")]
    pub image_codec: i32,
    /// disable upload of manifest file (default false)
    #[prost(bool, tag="7")]
    pub disable_manifest: bool,
    /// required
    #[prost(oneof="image_output::Output", tags="8, 9, 10, 11")]
    pub output: ::core::option::Option<image_output::Output>,
}
/// Nested message and enum types in `ImageOutput`.
pub mod image_output {
    /// required
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Output {
        #[prost(message, tag="8")]
        S3(super::S3Upload),
        #[prost(message, tag="9")]
        Gcp(super::GcpUpload),
        #[prost(message, tag="10")]
        Azure(super::AzureBlobUpload),
        #[prost(message, tag="11")]
        AliOss(super::AliOssUpload),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct S3Upload {
    #[prost(string, tag="1")]
    pub access_key: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub secret: ::prost::alloc::string::String,
    #[prost(string, tag="11")]
    pub session_token: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub region: ::prost::alloc::string::String,
    #[prost(string, tag="4")]
    pub endpoint: ::prost::alloc::string::String,
    #[prost(string, tag="5")]
    pub bucket: ::prost::alloc::string::String,
    #[prost(bool, tag="6")]
    pub force_path_style: bool,
    #[prost(map="string, string", tag="7")]
    pub metadata: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    #[prost(string, tag="8")]
    pub tagging: ::prost::alloc::string::String,
    /// Content-Disposition header
    #[prost(string, tag="9")]
    pub content_disposition: ::prost::alloc::string::String,
    #[prost(message, optional, tag="10")]
    pub proxy: ::core::option::Option<ProxyConfig>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GcpUpload {
    /// service account credentials serialized in JSON "credentials.json"
    #[prost(string, tag="1")]
    pub credentials: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub bucket: ::prost::alloc::string::String,
    #[prost(message, optional, tag="3")]
    pub proxy: ::core::option::Option<ProxyConfig>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AzureBlobUpload {
    #[prost(string, tag="1")]
    pub account_name: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub account_key: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub container_name: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AliOssUpload {
    #[prost(string, tag="1")]
    pub access_key: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub secret: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub region: ::prost::alloc::string::String,
    #[prost(string, tag="4")]
    pub endpoint: ::prost::alloc::string::String,
    #[prost(string, tag="5")]
    pub bucket: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ProxyConfig {
    #[prost(string, tag="1")]
    pub url: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub username: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub password: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StreamOutput {
    /// required
    #[prost(enumeration="StreamProtocol", tag="1")]
    pub protocol: i32,
    /// required
    #[prost(string, repeated, tag="2")]
    pub urls: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct EncodingOptions {
    /// (default 1920)
    #[prost(int32, tag="1")]
    pub width: i32,
    /// (default 1080)
    #[prost(int32, tag="2")]
    pub height: i32,
    /// (default 24)
    #[prost(int32, tag="3")]
    pub depth: i32,
    /// (default 30)
    #[prost(int32, tag="4")]
    pub framerate: i32,
    /// (default OPUS)
    #[prost(enumeration="AudioCodec", tag="5")]
    pub audio_codec: i32,
    /// (default 128)
    #[prost(int32, tag="6")]
    pub audio_bitrate: i32,
    /// quality setting on audio encoder
    #[prost(int32, tag="11")]
    pub audio_quality: i32,
    /// (default 44100)
    #[prost(int32, tag="7")]
    pub audio_frequency: i32,
    /// (default H264_MAIN)
    #[prost(enumeration="VideoCodec", tag="8")]
    pub video_codec: i32,
    /// (default 4500)
    #[prost(int32, tag="9")]
    pub video_bitrate: i32,
    /// quality setting on video encoder
    #[prost(int32, tag="12")]
    pub video_quality: i32,
    /// in seconds (default 4s for streaming, segment duration for segmented output, encoder default for files)
    #[prost(double, tag="10")]
    pub key_frame_interval: f64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateLayoutRequest {
    #[prost(string, tag="1")]
    pub egress_id: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub layout: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateStreamRequest {
    #[prost(string, tag="1")]
    pub egress_id: ::prost::alloc::string::String,
    #[prost(string, repeated, tag="2")]
    pub add_output_urls: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(string, repeated, tag="3")]
    pub remove_output_urls: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListEgressRequest {
    /// (optional, filter by room name)
    #[prost(string, tag="1")]
    pub room_name: ::prost::alloc::string::String,
    /// (optional, filter by egress ID)
    #[prost(string, tag="2")]
    pub egress_id: ::prost::alloc::string::String,
    /// (optional, list active egress only)
    #[prost(bool, tag="3")]
    pub active: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListEgressResponse {
    #[prost(message, repeated, tag="1")]
    pub items: ::prost::alloc::vec::Vec<EgressInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StopEgressRequest {
    #[prost(string, tag="1")]
    pub egress_id: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct EgressInfo {
    #[prost(string, tag="1")]
    pub egress_id: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub room_id: ::prost::alloc::string::String,
    #[prost(string, tag="13")]
    pub room_name: ::prost::alloc::string::String,
    #[prost(enumeration="EgressSourceType", tag="26")]
    pub source_type: i32,
    #[prost(enumeration="EgressStatus", tag="3")]
    pub status: i32,
    #[prost(int64, tag="10")]
    pub started_at: i64,
    #[prost(int64, tag="11")]
    pub ended_at: i64,
    #[prost(int64, tag="18")]
    pub updated_at: i64,
    #[prost(string, tag="21")]
    pub details: ::prost::alloc::string::String,
    #[prost(string, tag="9")]
    pub error: ::prost::alloc::string::String,
    #[prost(int32, tag="22")]
    pub error_code: i32,
    #[prost(message, repeated, tag="15")]
    pub stream_results: ::prost::alloc::vec::Vec<StreamInfo>,
    #[prost(message, repeated, tag="16")]
    pub file_results: ::prost::alloc::vec::Vec<FileInfo>,
    #[prost(message, repeated, tag="17")]
    pub segment_results: ::prost::alloc::vec::Vec<SegmentsInfo>,
    #[prost(message, repeated, tag="20")]
    pub image_results: ::prost::alloc::vec::Vec<ImagesInfo>,
    #[prost(string, tag="23")]
    pub manifest_location: ::prost::alloc::string::String,
    /// next ID: 27
    #[prost(bool, tag="25")]
    pub backup_storage_used: bool,
    #[prost(oneof="egress_info::Request", tags="4, 14, 19, 5, 6")]
    pub request: ::core::option::Option<egress_info::Request>,
    /// deprecated (use _result fields)
    #[prost(oneof="egress_info::Result", tags="7, 8, 12")]
    pub result: ::core::option::Option<egress_info::Result>,
}
/// Nested message and enum types in `EgressInfo`.
pub mod egress_info {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Request {
        #[prost(message, tag="4")]
        RoomComposite(super::RoomCompositeEgressRequest),
        #[prost(message, tag="14")]
        Web(super::WebEgressRequest),
        #[prost(message, tag="19")]
        Participant(super::ParticipantEgressRequest),
        #[prost(message, tag="5")]
        TrackComposite(super::TrackCompositeEgressRequest),
        #[prost(message, tag="6")]
        Track(super::TrackEgressRequest),
    }
    /// deprecated (use _result fields)
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Result {
        #[prost(message, tag="7")]
        Stream(super::StreamInfoList),
        #[prost(message, tag="8")]
        File(super::FileInfo),
        #[prost(message, tag="12")]
        Segments(super::SegmentsInfo),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StreamInfoList {
    #[prost(message, repeated, tag="1")]
    pub info: ::prost::alloc::vec::Vec<StreamInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StreamInfo {
    #[prost(string, tag="1")]
    pub url: ::prost::alloc::string::String,
    #[prost(int64, tag="2")]
    pub started_at: i64,
    #[prost(int64, tag="3")]
    pub ended_at: i64,
    #[prost(int64, tag="4")]
    pub duration: i64,
    #[prost(enumeration="stream_info::Status", tag="5")]
    pub status: i32,
    #[prost(string, tag="6")]
    pub error: ::prost::alloc::string::String,
}
/// Nested message and enum types in `StreamInfo`.
pub mod stream_info {
    #[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
    #[repr(i32)]
    pub enum Status {
        Active = 0,
        Finished = 1,
        Failed = 2,
    }
    impl Status {
        /// String value of the enum field names used in the ProtoBuf definition.
        ///
        /// The values are not transformed in any way and thus are considered stable
        /// (if the ProtoBuf definition does not change) and safe for programmatic use.
        pub fn as_str_name(&self) -> &'static str {
            match self {
                Status::Active => "ACTIVE",
                Status::Finished => "FINISHED",
                Status::Failed => "FAILED",
            }
        }
        /// Creates an enum from field names used in the ProtoBuf definition.
        pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
            match value {
                "ACTIVE" => Some(Self::Active),
                "FINISHED" => Some(Self::Finished),
                "FAILED" => Some(Self::Failed),
                _ => None,
            }
        }
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct FileInfo {
    #[prost(string, tag="1")]
    pub filename: ::prost::alloc::string::String,
    #[prost(int64, tag="2")]
    pub started_at: i64,
    #[prost(int64, tag="3")]
    pub ended_at: i64,
    #[prost(int64, tag="6")]
    pub duration: i64,
    #[prost(int64, tag="4")]
    pub size: i64,
    #[prost(string, tag="5")]
    pub location: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SegmentsInfo {
    #[prost(string, tag="1")]
    pub playlist_name: ::prost::alloc::string::String,
    #[prost(string, tag="8")]
    pub live_playlist_name: ::prost::alloc::string::String,
    #[prost(int64, tag="2")]
    pub duration: i64,
    #[prost(int64, tag="3")]
    pub size: i64,
    #[prost(string, tag="4")]
    pub playlist_location: ::prost::alloc::string::String,
    #[prost(string, tag="9")]
    pub live_playlist_location: ::prost::alloc::string::String,
    #[prost(int64, tag="5")]
    pub segment_count: i64,
    #[prost(int64, tag="6")]
    pub started_at: i64,
    #[prost(int64, tag="7")]
    pub ended_at: i64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ImagesInfo {
    #[prost(string, tag="4")]
    pub filename_prefix: ::prost::alloc::string::String,
    #[prost(int64, tag="1")]
    pub image_count: i64,
    #[prost(int64, tag="2")]
    pub started_at: i64,
    #[prost(int64, tag="3")]
    pub ended_at: i64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AutoParticipantEgress {
    #[prost(message, repeated, tag="3")]
    pub file_outputs: ::prost::alloc::vec::Vec<EncodedFileOutput>,
    #[prost(message, repeated, tag="4")]
    pub segment_outputs: ::prost::alloc::vec::Vec<SegmentedFileOutput>,
    #[prost(oneof="auto_participant_egress::Options", tags="1, 2")]
    pub options: ::core::option::Option<auto_participant_egress::Options>,
}
/// Nested message and enum types in `AutoParticipantEgress`.
pub mod auto_participant_egress {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Options {
        /// (default H264_720P_30)
        #[prost(enumeration="super::EncodingOptionsPreset", tag="1")]
        Preset(i32),
        /// (optional)
        #[prost(message, tag="2")]
        Advanced(super::EncodingOptions),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AutoTrackEgress {
    /// see docs for templating (default {track_id}-{time})
    #[prost(string, tag="1")]
    pub filepath: ::prost::alloc::string::String,
    /// disables upload of json manifest file (default false)
    #[prost(bool, tag="5")]
    pub disable_manifest: bool,
    #[prost(oneof="auto_track_egress::Output", tags="2, 3, 4, 6")]
    pub output: ::core::option::Option<auto_track_egress::Output>,
}
/// Nested message and enum types in `AutoTrackEgress`.
pub mod auto_track_egress {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Output {
        #[prost(message, tag="2")]
        S3(super::S3Upload),
        #[prost(message, tag="3")]
        Gcp(super::GcpUpload),
        #[prost(message, tag="4")]
        Azure(super::AzureBlobUpload),
        #[prost(message, tag="6")]
        AliOss(super::AliOssUpload),
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum EncodedFileType {
    /// file type chosen based on codecs
    DefaultFiletype = 0,
    Mp4 = 1,
    Ogg = 2,
}
impl EncodedFileType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            EncodedFileType::DefaultFiletype => "DEFAULT_FILETYPE",
            EncodedFileType::Mp4 => "MP4",
            EncodedFileType::Ogg => "OGG",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_FILETYPE" => Some(Self::DefaultFiletype),
            "MP4" => Some(Self::Mp4),
            "OGG" => Some(Self::Ogg),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum SegmentedFileProtocol {
    DefaultSegmentedFileProtocol = 0,
    HlsProtocol = 1,
}
impl SegmentedFileProtocol {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            SegmentedFileProtocol::DefaultSegmentedFileProtocol => "DEFAULT_SEGMENTED_FILE_PROTOCOL",
            SegmentedFileProtocol::HlsProtocol => "HLS_PROTOCOL",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_SEGMENTED_FILE_PROTOCOL" => Some(Self::DefaultSegmentedFileProtocol),
            "HLS_PROTOCOL" => Some(Self::HlsProtocol),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum SegmentedFileSuffix {
    Index = 0,
    Timestamp = 1,
}
impl SegmentedFileSuffix {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            SegmentedFileSuffix::Index => "INDEX",
            SegmentedFileSuffix::Timestamp => "TIMESTAMP",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "INDEX" => Some(Self::Index),
            "TIMESTAMP" => Some(Self::Timestamp),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum ImageFileSuffix {
    ImageSuffixIndex = 0,
    ImageSuffixTimestamp = 1,
    /// Do not append any suffix and overwrite the existing image with the latest
    ImageSuffixNoneOverwrite = 2,
}
impl ImageFileSuffix {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            ImageFileSuffix::ImageSuffixIndex => "IMAGE_SUFFIX_INDEX",
            ImageFileSuffix::ImageSuffixTimestamp => "IMAGE_SUFFIX_TIMESTAMP",
            ImageFileSuffix::ImageSuffixNoneOverwrite => "IMAGE_SUFFIX_NONE_OVERWRITE",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "IMAGE_SUFFIX_INDEX" => Some(Self::ImageSuffixIndex),
            "IMAGE_SUFFIX_TIMESTAMP" => Some(Self::ImageSuffixTimestamp),
            "IMAGE_SUFFIX_NONE_OVERWRITE" => Some(Self::ImageSuffixNoneOverwrite),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum StreamProtocol {
    /// protocol chosen based on urls
    DefaultProtocol = 0,
    Rtmp = 1,
    Srt = 2,
}
impl StreamProtocol {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            StreamProtocol::DefaultProtocol => "DEFAULT_PROTOCOL",
            StreamProtocol::Rtmp => "RTMP",
            StreamProtocol::Srt => "SRT",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_PROTOCOL" => Some(Self::DefaultProtocol),
            "RTMP" => Some(Self::Rtmp),
            "SRT" => Some(Self::Srt),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum AudioMixing {
    /// all users are mixed together
    DefaultMixing = 0,
    /// agent audio in the left channel, all other audio in the right channel
    DualChannelAgent = 1,
    /// each new audio track alternates between left and right channels
    DualChannelAlternate = 2,
}
impl AudioMixing {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            AudioMixing::DefaultMixing => "DEFAULT_MIXING",
            AudioMixing::DualChannelAgent => "DUAL_CHANNEL_AGENT",
            AudioMixing::DualChannelAlternate => "DUAL_CHANNEL_ALTERNATE",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "DEFAULT_MIXING" => Some(Self::DefaultMixing),
            "DUAL_CHANNEL_AGENT" => Some(Self::DualChannelAgent),
            "DUAL_CHANNEL_ALTERNATE" => Some(Self::DualChannelAlternate),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum EncodingOptionsPreset {
    ///   1280x720, 30fps, 3000kpbs, H.264_MAIN / OPUS
    H264720p30 = 0,
    ///   1280x720, 60fps, 4500kbps, H.264_MAIN / OPUS
    H264720p60 = 1,
    /// 1920x1080, 30fps, 4500kbps, H.264_MAIN / OPUS
    H2641080p30 = 2,
    /// 1920x1080, 60fps, 6000kbps, H.264_MAIN / OPUS
    H2641080p60 = 3,
    ///   720x1280, 30fps, 3000kpbs, H.264_MAIN / OPUS
    PortraitH264720p30 = 4,
    ///   720x1280, 60fps, 4500kbps, H.264_MAIN / OPUS
    PortraitH264720p60 = 5,
    /// 1080x1920, 30fps, 4500kbps, H.264_MAIN / OPUS
    PortraitH2641080p30 = 6,
    /// 1080x1920, 60fps, 6000kbps, H.264_MAIN / OPUS
    PortraitH2641080p60 = 7,
}
impl EncodingOptionsPreset {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            EncodingOptionsPreset::H264720p30 => "H264_720P_30",
            EncodingOptionsPreset::H264720p60 => "H264_720P_60",
            EncodingOptionsPreset::H2641080p30 => "H264_1080P_30",
            EncodingOptionsPreset::H2641080p60 => "H264_1080P_60",
            EncodingOptionsPreset::PortraitH264720p30 => "PORTRAIT_H264_720P_30",
            EncodingOptionsPreset::PortraitH264720p60 => "PORTRAIT_H264_720P_60",
            EncodingOptionsPreset::PortraitH2641080p30 => "PORTRAIT_H264_1080P_30",
            EncodingOptionsPreset::PortraitH2641080p60 => "PORTRAIT_H264_1080P_60",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "H264_720P_30" => Some(Self::H264720p30),
            "H264_720P_60" => Some(Self::H264720p60),
            "H264_1080P_30" => Some(Self::H2641080p30),
            "H264_1080P_60" => Some(Self::H2641080p60),
            "PORTRAIT_H264_720P_30" => Some(Self::PortraitH264720p30),
            "PORTRAIT_H264_720P_60" => Some(Self::PortraitH264720p60),
            "PORTRAIT_H264_1080P_30" => Some(Self::PortraitH2641080p30),
            "PORTRAIT_H264_1080P_60" => Some(Self::PortraitH2641080p60),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum EgressStatus {
    EgressStarting = 0,
    EgressActive = 1,
    EgressEnding = 2,
    EgressComplete = 3,
    EgressFailed = 4,
    EgressAborted = 5,
    EgressLimitReached = 6,
}
impl EgressStatus {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            EgressStatus::EgressStarting => "EGRESS_STARTING",
            EgressStatus::EgressActive => "EGRESS_ACTIVE",
            EgressStatus::EgressEnding => "EGRESS_ENDING",
            EgressStatus::EgressComplete => "EGRESS_COMPLETE",
            EgressStatus::EgressFailed => "EGRESS_FAILED",
            EgressStatus::EgressAborted => "EGRESS_ABORTED",
            EgressStatus::EgressLimitReached => "EGRESS_LIMIT_REACHED",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "EGRESS_STARTING" => Some(Self::EgressStarting),
            "EGRESS_ACTIVE" => Some(Self::EgressActive),
            "EGRESS_ENDING" => Some(Self::EgressEnding),
            "EGRESS_COMPLETE" => Some(Self::EgressComplete),
            "EGRESS_FAILED" => Some(Self::EgressFailed),
            "EGRESS_ABORTED" => Some(Self::EgressAborted),
            "EGRESS_LIMIT_REACHED" => Some(Self::EgressLimitReached),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum EgressSourceType {
    Web = 0,
    Sdk = 1,
}
impl EgressSourceType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            EgressSourceType::Web => "EGRESS_SOURCE_TYPE_WEB",
            EgressSourceType::Sdk => "EGRESS_SOURCE_TYPE_SDK",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "EGRESS_SOURCE_TYPE_WEB" => Some(Self::Web),
            "EGRESS_SOURCE_TYPE_SDK" => Some(Self::Sdk),
            _ => None,
        }
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SignalRequest {
    #[prost(oneof="signal_request::Message", tags="1, 2, 3, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18")]
    pub message: ::core::option::Option<signal_request::Message>,
}
/// Nested message and enum types in `SignalRequest`.
pub mod signal_request {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Message {
        /// initial join exchange, for publisher
        #[prost(message, tag="1")]
        Offer(super::SessionDescription),
        /// participant answering publisher offer
        #[prost(message, tag="2")]
        Answer(super::SessionDescription),
        #[prost(message, tag="3")]
        Trickle(super::TrickleRequest),
        #[prost(message, tag="4")]
        AddTrack(super::AddTrackRequest),
        /// mute the participant's published tracks
        #[prost(message, tag="5")]
        Mute(super::MuteTrackRequest),
        /// Subscribe or unsubscribe from tracks
        #[prost(message, tag="6")]
        Subscription(super::UpdateSubscription),
        /// Update settings of subscribed tracks
        #[prost(message, tag="7")]
        TrackSetting(super::UpdateTrackSettings),
        /// Immediately terminate session
        #[prost(message, tag="8")]
        Leave(super::LeaveRequest),
        /// Update published video layers
        #[prost(message, tag="10")]
        UpdateLayers(super::UpdateVideoLayers),
        /// Update subscriber permissions
        #[prost(message, tag="11")]
        SubscriptionPermission(super::SubscriptionPermission),
        /// sync client's subscribe state to server during reconnect
        #[prost(message, tag="12")]
        SyncState(super::SyncState),
        /// Simulate conditions, for client validations
        #[prost(message, tag="13")]
        Simulate(super::SimulateScenario),
        /// client triggered ping to server
        ///
        /// deprecated by ping_req (message Ping)
        #[prost(int64, tag="14")]
        Ping(i64),
        /// update a participant's own metadata, name, or attributes
        /// requires canUpdateOwnParticipantMetadata permission
        #[prost(message, tag="15")]
        UpdateMetadata(super::UpdateParticipantMetadata),
        #[prost(message, tag="16")]
        PingReq(super::Ping),
        /// Update local audio track settings
        #[prost(message, tag="17")]
        UpdateAudioTrack(super::UpdateLocalAudioTrack),
        /// Update local video track settings
        #[prost(message, tag="18")]
        UpdateVideoTrack(super::UpdateLocalVideoTrack),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SignalResponse {
    #[prost(oneof="signal_response::Message", tags="1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24")]
    pub message: ::core::option::Option<signal_response::Message>,
}
/// Nested message and enum types in `SignalResponse`.
pub mod signal_response {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Message {
        /// sent when join is accepted
        #[prost(message, tag="1")]
        Join(super::JoinResponse),
        /// sent when server answers publisher
        #[prost(message, tag="2")]
        Answer(super::SessionDescription),
        /// sent when server is sending subscriber an offer
        #[prost(message, tag="3")]
        Offer(super::SessionDescription),
        /// sent when an ICE candidate is available
        #[prost(message, tag="4")]
        Trickle(super::TrickleRequest),
        /// sent when participants in the room has changed
        #[prost(message, tag="5")]
        Update(super::ParticipantUpdate),
        /// sent to the participant when their track has been published
        #[prost(message, tag="6")]
        TrackPublished(super::TrackPublishedResponse),
        /// Immediately terminate session
        #[prost(message, tag="8")]
        Leave(super::LeaveRequest),
        /// server initiated mute
        #[prost(message, tag="9")]
        Mute(super::MuteTrackRequest),
        /// indicates changes to speaker status, including when they've gone to not speaking
        #[prost(message, tag="10")]
        SpeakersChanged(super::SpeakersChanged),
        /// sent when metadata of the room has changed
        #[prost(message, tag="11")]
        RoomUpdate(super::RoomUpdate),
        /// when connection quality changed
        #[prost(message, tag="12")]
        ConnectionQuality(super::ConnectionQualityUpdate),
        /// when streamed tracks state changed, used to notify when any of the streams were paused due to
        /// congestion
        #[prost(message, tag="13")]
        StreamStateUpdate(super::StreamStateUpdate),
        /// when max subscribe quality changed, used by dynamic broadcasting to disable unused layers
        #[prost(message, tag="14")]
        SubscribedQualityUpdate(super::SubscribedQualityUpdate),
        /// when subscription permission changed
        #[prost(message, tag="15")]
        SubscriptionPermissionUpdate(super::SubscriptionPermissionUpdate),
        /// update the token the client was using, to prevent an active client from using an expired token
        #[prost(string, tag="16")]
        RefreshToken(::prost::alloc::string::String),
        /// server initiated track unpublish
        #[prost(message, tag="17")]
        TrackUnpublished(super::TrackUnpublishedResponse),
        /// respond to ping
        ///
        /// deprecated by pong_resp (message Pong)
        #[prost(int64, tag="18")]
        Pong(i64),
        /// sent when client reconnects
        #[prost(message, tag="19")]
        Reconnect(super::ReconnectResponse),
        /// respond to Ping
        #[prost(message, tag="20")]
        PongResp(super::Pong),
        /// Subscription response, client should not expect any media from this subscription if it fails
        #[prost(message, tag="21")]
        SubscriptionResponse(super::SubscriptionResponse),
        /// Response relating to user inititated requests that carry a `request_id`
        #[prost(message, tag="22")]
        RequestResponse(super::RequestResponse),
        /// notify to the publisher when a published track has been subscribed for the first time
        #[prost(message, tag="23")]
        TrackSubscribed(super::TrackSubscribed),
        /// notify to the participant when they have been moved to a new room
        #[prost(message, tag="24")]
        RoomMoved(super::RoomMovedResponse),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SimulcastCodec {
    #[prost(string, tag="1")]
    pub codec: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub cid: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AddTrackRequest {
    /// client ID of track, to match it when RTC track is received
    #[prost(string, tag="1")]
    pub cid: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub name: ::prost::alloc::string::String,
    #[prost(enumeration="TrackType", tag="3")]
    pub r#type: i32,
    /// to be deprecated in favor of layers
    #[prost(uint32, tag="4")]
    pub width: u32,
    #[prost(uint32, tag="5")]
    pub height: u32,
    /// true to add track and initialize to muted
    #[prost(bool, tag="6")]
    pub muted: bool,
    /// true if DTX (Discontinuous Transmission) is disabled for audio
    ///
    /// deprecated in favor of audio_features
    #[deprecated]
    #[prost(bool, tag="7")]
    pub disable_dtx: bool,
    #[prost(enumeration="TrackSource", tag="8")]
    pub source: i32,
    #[prost(message, repeated, tag="9")]
    pub layers: ::prost::alloc::vec::Vec<VideoLayer>,
    #[prost(message, repeated, tag="10")]
    pub simulcast_codecs: ::prost::alloc::vec::Vec<SimulcastCodec>,
    /// server ID of track, publish new codec to exist track
    #[prost(string, tag="11")]
    pub sid: ::prost::alloc::string::String,
    /// deprecated in favor of audio_features
    #[deprecated]
    #[prost(bool, tag="12")]
    pub stereo: bool,
    /// true if RED (Redundant Encoding) is disabled for audio
    #[prost(bool, tag="13")]
    pub disable_red: bool,
    #[prost(enumeration="encryption::Type", tag="14")]
    pub encryption: i32,
    /// which stream the track belongs to, used to group tracks together.
    /// if not specified, server will infer it from track source to bundle camera/microphone, screenshare/audio together
    #[prost(string, tag="15")]
    pub stream: ::prost::alloc::string::String,
    #[prost(enumeration="BackupCodecPolicy", tag="16")]
    pub backup_codec_policy: i32,
    #[prost(enumeration="AudioTrackFeature", repeated, tag="17")]
    pub audio_features: ::prost::alloc::vec::Vec<i32>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TrickleRequest {
    #[prost(string, tag="1")]
    pub candidate_init: ::prost::alloc::string::String,
    #[prost(enumeration="SignalTarget", tag="2")]
    pub target: i32,
    #[prost(bool, tag="3")]
    pub r#final: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct MuteTrackRequest {
    #[prost(string, tag="1")]
    pub sid: ::prost::alloc::string::String,
    #[prost(bool, tag="2")]
    pub muted: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct JoinResponse {
    #[prost(message, optional, tag="1")]
    pub room: ::core::option::Option<Room>,
    #[prost(message, optional, tag="2")]
    pub participant: ::core::option::Option<ParticipantInfo>,
    #[prost(message, repeated, tag="3")]
    pub other_participants: ::prost::alloc::vec::Vec<ParticipantInfo>,
    /// deprecated. use server_info.version instead.
    #[prost(string, tag="4")]
    pub server_version: ::prost::alloc::string::String,
    #[prost(message, repeated, tag="5")]
    pub ice_servers: ::prost::alloc::vec::Vec<IceServer>,
    /// use subscriber as the primary PeerConnection
    #[prost(bool, tag="6")]
    pub subscriber_primary: bool,
    /// when the current server isn't available, return alternate url to retry connection
    /// when this is set, the other fields will be largely empty
    #[prost(string, tag="7")]
    pub alternative_url: ::prost::alloc::string::String,
    #[prost(message, optional, tag="8")]
    pub client_configuration: ::core::option::Option<ClientConfiguration>,
    /// deprecated. use server_info.region instead.
    #[prost(string, tag="9")]
    pub server_region: ::prost::alloc::string::String,
    #[prost(int32, tag="10")]
    pub ping_timeout: i32,
    #[prost(int32, tag="11")]
    pub ping_interval: i32,
    #[prost(message, optional, tag="12")]
    pub server_info: ::core::option::Option<ServerInfo>,
    /// Server-Injected-Frame byte trailer, used to identify unencrypted frames when e2ee is enabled
    #[prost(bytes="vec", tag="13")]
    pub sif_trailer: ::prost::alloc::vec::Vec<u8>,
    #[prost(message, repeated, tag="14")]
    pub enabled_publish_codecs: ::prost::alloc::vec::Vec<Codec>,
    /// when set, client should attempt to establish publish peer connection when joining room to speed up publishing
    #[prost(bool, tag="15")]
    pub fast_publish: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ReconnectResponse {
    #[prost(message, repeated, tag="1")]
    pub ice_servers: ::prost::alloc::vec::Vec<IceServer>,
    #[prost(message, optional, tag="2")]
    pub client_configuration: ::core::option::Option<ClientConfiguration>,
    #[prost(message, optional, tag="3")]
    pub server_info: ::core::option::Option<ServerInfo>,
    /// last sequence number of reliable message received before resuming
    #[prost(uint32, tag="4")]
    pub last_message_seq: u32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TrackPublishedResponse {
    #[prost(string, tag="1")]
    pub cid: ::prost::alloc::string::String,
    #[prost(message, optional, tag="2")]
    pub track: ::core::option::Option<TrackInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TrackUnpublishedResponse {
    #[prost(string, tag="1")]
    pub track_sid: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SessionDescription {
    /// "answer" | "offer" | "pranswer" | "rollback"
    #[prost(string, tag="1")]
    pub r#type: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub sdp: ::prost::alloc::string::String,
    #[prost(uint32, tag="3")]
    pub id: u32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ParticipantUpdate {
    #[prost(message, repeated, tag="1")]
    pub participants: ::prost::alloc::vec::Vec<ParticipantInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateSubscription {
    #[prost(string, repeated, tag="1")]
    pub track_sids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(bool, tag="2")]
    pub subscribe: bool,
    #[prost(message, repeated, tag="3")]
    pub participant_tracks: ::prost::alloc::vec::Vec<ParticipantTracks>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateTrackSettings {
    #[prost(string, repeated, tag="1")]
    pub track_sids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// when true, the track is placed in a paused state, with no new data returned
    #[prost(bool, tag="3")]
    pub disabled: bool,
    /// deprecated in favor of width & height
    #[prost(enumeration="VideoQuality", tag="4")]
    pub quality: i32,
    /// for video, width to receive
    #[prost(uint32, tag="5")]
    pub width: u32,
    /// for video, height to receive
    #[prost(uint32, tag="6")]
    pub height: u32,
    #[prost(uint32, tag="7")]
    pub fps: u32,
    /// subscription priority. 1 being the highest (0 is unset)
    /// when unset, server sill assign priority based on the order of subscription
    /// server will use priority in the following ways:
    /// 1. when subscribed tracks exceed per-participant subscription limit, server will
    ///     pause the lowest priority tracks
    /// 2. when the network is congested, server will assign available bandwidth to
    ///     higher priority tracks first. lowest priority tracks can be paused
    #[prost(uint32, tag="8")]
    pub priority: u32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateLocalAudioTrack {
    #[prost(string, tag="1")]
    pub track_sid: ::prost::alloc::string::String,
    #[prost(enumeration="AudioTrackFeature", repeated, tag="2")]
    pub features: ::prost::alloc::vec::Vec<i32>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateLocalVideoTrack {
    #[prost(string, tag="1")]
    pub track_sid: ::prost::alloc::string::String,
    #[prost(uint32, tag="2")]
    pub width: u32,
    #[prost(uint32, tag="3")]
    pub height: u32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct LeaveRequest {
    /// sent when server initiates the disconnect due to server-restart
    /// indicates clients should attempt full-reconnect sequence
    /// NOTE: `can_reconnect` obsoleted by `action` starting in protocol version 13
    #[prost(bool, tag="1")]
    pub can_reconnect: bool,
    #[prost(enumeration="DisconnectReason", tag="2")]
    pub reason: i32,
    #[prost(enumeration="leave_request::Action", tag="3")]
    pub action: i32,
    #[prost(message, optional, tag="4")]
    pub regions: ::core::option::Option<RegionSettings>,
}
/// Nested message and enum types in `LeaveRequest`.
pub mod leave_request {
    /// indicates action clients should take on receiving this message
    #[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
    #[repr(i32)]
    pub enum Action {
        /// should disconnect
        Disconnect = 0,
        /// should attempt a resume with `reconnect=1` in join URL
        Resume = 1,
        /// should attempt a reconnect, i. e. no `reconnect=1`
        Reconnect = 2,
    }
    impl Action {
        /// String value of the enum field names used in the ProtoBuf definition.
        ///
        /// The values are not transformed in any way and thus are considered stable
        /// (if the ProtoBuf definition does not change) and safe for programmatic use.
        pub fn as_str_name(&self) -> &'static str {
            match self {
                Action::Disconnect => "DISCONNECT",
                Action::Resume => "RESUME",
                Action::Reconnect => "RECONNECT",
            }
        }
        /// Creates an enum from field names used in the ProtoBuf definition.
        pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
            match value {
                "DISCONNECT" => Some(Self::Disconnect),
                "RESUME" => Some(Self::Resume),
                "RECONNECT" => Some(Self::Reconnect),
                _ => None,
            }
        }
    }
}
/// message to indicate published video track dimensions are changing
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateVideoLayers {
    #[prost(string, tag="1")]
    pub track_sid: ::prost::alloc::string::String,
    #[prost(message, repeated, tag="2")]
    pub layers: ::prost::alloc::vec::Vec<VideoLayer>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateParticipantMetadata {
    #[prost(string, tag="1")]
    pub metadata: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub name: ::prost::alloc::string::String,
    /// attributes to update. it only updates attributes that have been set
    /// to delete attributes, set the value to an empty string
    #[prost(map="string, string", tag="3")]
    pub attributes: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    #[prost(uint32, tag="4")]
    pub request_id: u32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct IceServer {
    #[prost(string, repeated, tag="1")]
    pub urls: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(string, tag="2")]
    pub username: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub credential: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SpeakersChanged {
    #[prost(message, repeated, tag="1")]
    pub speakers: ::prost::alloc::vec::Vec<SpeakerInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RoomUpdate {
    #[prost(message, optional, tag="1")]
    pub room: ::core::option::Option<Room>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ConnectionQualityInfo {
    #[prost(string, tag="1")]
    pub participant_sid: ::prost::alloc::string::String,
    #[prost(enumeration="ConnectionQuality", tag="2")]
    pub quality: i32,
    #[prost(float, tag="3")]
    pub score: f32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ConnectionQualityUpdate {
    #[prost(message, repeated, tag="1")]
    pub updates: ::prost::alloc::vec::Vec<ConnectionQualityInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StreamStateInfo {
    #[prost(string, tag="1")]
    pub participant_sid: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub track_sid: ::prost::alloc::string::String,
    #[prost(enumeration="StreamState", tag="3")]
    pub state: i32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct StreamStateUpdate {
    #[prost(message, repeated, tag="1")]
    pub stream_states: ::prost::alloc::vec::Vec<StreamStateInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribedQuality {
    #[prost(enumeration="VideoQuality", tag="1")]
    pub quality: i32,
    #[prost(bool, tag="2")]
    pub enabled: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribedCodec {
    #[prost(string, tag="1")]
    pub codec: ::prost::alloc::string::String,
    #[prost(message, repeated, tag="2")]
    pub qualities: ::prost::alloc::vec::Vec<SubscribedQuality>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribedQualityUpdate {
    #[prost(string, tag="1")]
    pub track_sid: ::prost::alloc::string::String,
    #[deprecated]
    #[prost(message, repeated, tag="2")]
    pub subscribed_qualities: ::prost::alloc::vec::Vec<SubscribedQuality>,
    #[prost(message, repeated, tag="3")]
    pub subscribed_codecs: ::prost::alloc::vec::Vec<SubscribedCodec>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TrackPermission {
    /// permission could be granted either by participant sid or identity
    #[prost(string, tag="1")]
    pub participant_sid: ::prost::alloc::string::String,
    #[prost(bool, tag="2")]
    pub all_tracks: bool,
    #[prost(string, repeated, tag="3")]
    pub track_sids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(string, tag="4")]
    pub participant_identity: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscriptionPermission {
    #[prost(bool, tag="1")]
    pub all_participants: bool,
    #[prost(message, repeated, tag="2")]
    pub track_permissions: ::prost::alloc::vec::Vec<TrackPermission>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscriptionPermissionUpdate {
    #[prost(string, tag="1")]
    pub participant_sid: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub track_sid: ::prost::alloc::string::String,
    #[prost(bool, tag="3")]
    pub allowed: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RoomMovedResponse {
    /// information about the new room
    #[prost(message, optional, tag="1")]
    pub room: ::core::option::Option<Room>,
    /// new reconnect token that can be used to reconnect to the new room
    #[prost(string, tag="2")]
    pub token: ::prost::alloc::string::String,
    #[prost(message, optional, tag="3")]
    pub participant: ::core::option::Option<ParticipantInfo>,
    #[prost(message, repeated, tag="4")]
    pub other_participants: ::prost::alloc::vec::Vec<ParticipantInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SyncState {
    /// last subscribe answer before reconnecting
    #[prost(message, optional, tag="1")]
    pub answer: ::core::option::Option<SessionDescription>,
    #[prost(message, optional, tag="2")]
    pub subscription: ::core::option::Option<UpdateSubscription>,
    #[prost(message, repeated, tag="3")]
    pub publish_tracks: ::prost::alloc::vec::Vec<TrackPublishedResponse>,
    #[prost(message, repeated, tag="4")]
    pub data_channels: ::prost::alloc::vec::Vec<DataChannelInfo>,
    /// last received server side offer before reconnecting
    #[prost(message, optional, tag="5")]
    pub offer: ::core::option::Option<SessionDescription>,
    #[prost(string, repeated, tag="6")]
    pub track_sids_disabled: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(message, repeated, tag="7")]
    pub datachannel_receive_states: ::prost::alloc::vec::Vec<DataChannelReceiveState>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DataChannelReceiveState {
    #[prost(string, tag="1")]
    pub publisher_sid: ::prost::alloc::string::String,
    #[prost(uint32, tag="2")]
    pub last_seq: u32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DataChannelInfo {
    #[prost(string, tag="1")]
    pub label: ::prost::alloc::string::String,
    #[prost(uint32, tag="2")]
    pub id: u32,
    #[prost(enumeration="SignalTarget", tag="3")]
    pub target: i32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SimulateScenario {
    #[prost(oneof="simulate_scenario::Scenario", tags="1, 2, 3, 4, 5, 6, 7, 8, 9")]
    pub scenario: ::core::option::Option<simulate_scenario::Scenario>,
}
/// Nested message and enum types in `SimulateScenario`.
pub mod simulate_scenario {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Scenario {
        /// simulate N seconds of speaker activity
        #[prost(int32, tag="1")]
        SpeakerUpdate(i32),
        /// simulate local node failure
        #[prost(bool, tag="2")]
        NodeFailure(bool),
        /// simulate migration
        #[prost(bool, tag="3")]
        Migration(bool),
        /// server to send leave
        #[prost(bool, tag="4")]
        ServerLeave(bool),
        /// switch candidate protocol to tcp
        #[prost(enumeration="super::CandidateProtocol", tag="5")]
        SwitchCandidateProtocol(i32),
        /// maximum bandwidth for subscribers, in bps
        /// when zero, clears artificial bandwidth limit
        #[prost(int64, tag="6")]
        SubscriberBandwidth(i64),
        /// disconnect signal on resume
        #[prost(bool, tag="7")]
        DisconnectSignalOnResume(bool),
        /// disconnect signal on resume before sending any messages from server
        #[prost(bool, tag="8")]
        DisconnectSignalOnResumeNoMessages(bool),
        /// full reconnect leave request
        #[prost(bool, tag="9")]
        LeaveRequestFullReconnect(bool),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Ping {
    #[prost(int64, tag="1")]
    pub timestamp: i64,
    /// rtt in milliseconds calculated by client
    #[prost(int64, tag="2")]
    pub rtt: i64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Pong {
    /// timestamp field of last received ping request
    #[prost(int64, tag="1")]
    pub last_ping_timestamp: i64,
    #[prost(int64, tag="2")]
    pub timestamp: i64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RegionSettings {
    #[prost(message, repeated, tag="1")]
    pub regions: ::prost::alloc::vec::Vec<RegionInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RegionInfo {
    #[prost(string, tag="1")]
    pub region: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub url: ::prost::alloc::string::String,
    #[prost(int64, tag="3")]
    pub distance: i64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscriptionResponse {
    #[prost(string, tag="1")]
    pub track_sid: ::prost::alloc::string::String,
    #[prost(enumeration="SubscriptionError", tag="2")]
    pub err: i32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RequestResponse {
    #[prost(uint32, tag="1")]
    pub request_id: u32,
    #[prost(enumeration="request_response::Reason", tag="2")]
    pub reason: i32,
    #[prost(string, tag="3")]
    pub message: ::prost::alloc::string::String,
}
/// Nested message and enum types in `RequestResponse`.
pub mod request_response {
    #[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
    #[repr(i32)]
    pub enum Reason {
        Ok = 0,
        NotFound = 1,
        NotAllowed = 2,
        LimitExceeded = 3,
    }
    impl Reason {
        /// String value of the enum field names used in the ProtoBuf definition.
        ///
        /// The values are not transformed in any way and thus are considered stable
        /// (if the ProtoBuf definition does not change) and safe for programmatic use.
        pub fn as_str_name(&self) -> &'static str {
            match self {
                Reason::Ok => "OK",
                Reason::NotFound => "NOT_FOUND",
                Reason::NotAllowed => "NOT_ALLOWED",
                Reason::LimitExceeded => "LIMIT_EXCEEDED",
            }
        }
        /// Creates an enum from field names used in the ProtoBuf definition.
        pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
            match value {
                "OK" => Some(Self::Ok),
                "NOT_FOUND" => Some(Self::NotFound),
                "NOT_ALLOWED" => Some(Self::NotAllowed),
                "LIMIT_EXCEEDED" => Some(Self::LimitExceeded),
                _ => None,
            }
        }
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TrackSubscribed {
    #[prost(string, tag="1")]
    pub track_sid: ::prost::alloc::string::String,
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum SignalTarget {
    Publisher = 0,
    Subscriber = 1,
}
impl SignalTarget {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            SignalTarget::Publisher => "PUBLISHER",
            SignalTarget::Subscriber => "SUBSCRIBER",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "PUBLISHER" => Some(Self::Publisher),
            "SUBSCRIBER" => Some(Self::Subscriber),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum StreamState {
    Active = 0,
    Paused = 1,
}
impl StreamState {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            StreamState::Active => "ACTIVE",
            StreamState::Paused => "PAUSED",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "ACTIVE" => Some(Self::Active),
            "PAUSED" => Some(Self::Paused),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum CandidateProtocol {
    Udp = 0,
    Tcp = 1,
    Tls = 2,
}
impl CandidateProtocol {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            CandidateProtocol::Udp => "UDP",
            CandidateProtocol::Tcp => "TCP",
            CandidateProtocol::Tls => "TLS",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "UDP" => Some(Self::Udp),
            "TCP" => Some(Self::Tcp),
            "TLS" => Some(Self::Tls),
            _ => None,
        }
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Job {
    #[prost(string, tag="1")]
    pub id: ::prost::alloc::string::String,
    #[prost(string, tag="9")]
    pub dispatch_id: ::prost::alloc::string::String,
    #[prost(enumeration="JobType", tag="2")]
    pub r#type: i32,
    #[prost(message, optional, tag="3")]
    pub room: ::core::option::Option<Room>,
    #[prost(message, optional, tag="4")]
    pub participant: ::core::option::Option<ParticipantInfo>,
    #[deprecated]
    #[prost(string, tag="5")]
    pub namespace: ::prost::alloc::string::String,
    #[prost(string, tag="6")]
    pub metadata: ::prost::alloc::string::String,
    #[prost(string, tag="7")]
    pub agent_name: ::prost::alloc::string::String,
    #[prost(message, optional, tag="8")]
    pub state: ::core::option::Option<JobState>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct JobState {
    #[prost(enumeration="JobStatus", tag="1")]
    pub status: i32,
    #[prost(string, tag="2")]
    pub error: ::prost::alloc::string::String,
    #[prost(int64, tag="3")]
    pub started_at: i64,
    #[prost(int64, tag="4")]
    pub ended_at: i64,
    #[prost(int64, tag="5")]
    pub updated_at: i64,
    #[prost(string, tag="6")]
    pub participant_identity: ::prost::alloc::string::String,
}
/// from Worker to Server
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct WorkerMessage {
    #[prost(oneof="worker_message::Message", tags="1, 2, 3, 4, 5, 6, 7")]
    pub message: ::core::option::Option<worker_message::Message>,
}
/// Nested message and enum types in `WorkerMessage`.
pub mod worker_message {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Message {
        /// agent workers need to register themselves with the server first
        #[prost(message, tag="1")]
        Register(super::RegisterWorkerRequest),
        /// worker confirms to server that it's available for a job, or declines it
        #[prost(message, tag="2")]
        Availability(super::AvailabilityResponse),
        /// worker can update its status to the server, including taking itself out of the pool
        #[prost(message, tag="3")]
        UpdateWorker(super::UpdateWorkerStatus),
        /// job can send status updates to the server, useful for tracking progress
        #[prost(message, tag="4")]
        UpdateJob(super::UpdateJobStatus),
        #[prost(message, tag="5")]
        Ping(super::WorkerPing),
        #[prost(message, tag="6")]
        SimulateJob(super::SimulateJobRequest),
        #[prost(message, tag="7")]
        MigrateJob(super::MigrateJobRequest),
    }
}
/// from Server to Worker
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ServerMessage {
    #[prost(oneof="server_message::Message", tags="1, 2, 3, 5, 4")]
    pub message: ::core::option::Option<server_message::Message>,
}
/// Nested message and enum types in `ServerMessage`.
pub mod server_message {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Message {
        /// server confirms the registration, from this moment on, the worker is considered active
        #[prost(message, tag="1")]
        Register(super::RegisterWorkerResponse),
        /// server asks worker to confirm availability for a job
        #[prost(message, tag="2")]
        Availability(super::AvailabilityRequest),
        #[prost(message, tag="3")]
        Assignment(super::JobAssignment),
        #[prost(message, tag="5")]
        Termination(super::JobTermination),
        #[prost(message, tag="4")]
        Pong(super::WorkerPong),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SimulateJobRequest {
    #[prost(enumeration="JobType", tag="1")]
    pub r#type: i32,
    #[prost(message, optional, tag="2")]
    pub room: ::core::option::Option<Room>,
    #[prost(message, optional, tag="3")]
    pub participant: ::core::option::Option<ParticipantInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct WorkerPing {
    #[prost(int64, tag="1")]
    pub timestamp: i64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct WorkerPong {
    #[prost(int64, tag="1")]
    pub last_timestamp: i64,
    #[prost(int64, tag="2")]
    pub timestamp: i64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RegisterWorkerRequest {
    #[prost(enumeration="JobType", tag="1")]
    pub r#type: i32,
    #[prost(string, tag="8")]
    pub agent_name: ::prost::alloc::string::String,
    /// string worker_id = 2;
    #[prost(string, tag="3")]
    pub version: ::prost::alloc::string::String,
    /// string name = 4 \[deprecated = true\];
    #[prost(uint32, tag="5")]
    pub ping_interval: u32,
    #[prost(string, optional, tag="6")]
    pub namespace: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(message, optional, tag="7")]
    pub allowed_permissions: ::core::option::Option<ParticipantPermission>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RegisterWorkerResponse {
    #[prost(string, tag="1")]
    pub worker_id: ::prost::alloc::string::String,
    #[prost(message, optional, tag="3")]
    pub server_info: ::core::option::Option<ServerInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct MigrateJobRequest {
    /// string job_id = 1 \[deprecated = true\];
    #[prost(string, repeated, tag="2")]
    pub job_ids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AvailabilityRequest {
    #[prost(message, optional, tag="1")]
    pub job: ::core::option::Option<Job>,
    /// True when the job was previously assigned to another worker but has been
    /// migrated due to different reasons (e.g. worker failure, job migration)
    #[prost(bool, tag="2")]
    pub resuming: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AvailabilityResponse {
    #[prost(string, tag="1")]
    pub job_id: ::prost::alloc::string::String,
    #[prost(bool, tag="2")]
    pub available: bool,
    #[prost(bool, tag="3")]
    pub supports_resume: bool,
    #[prost(bool, tag="8")]
    pub terminate: bool,
    #[prost(string, tag="4")]
    pub participant_name: ::prost::alloc::string::String,
    #[prost(string, tag="5")]
    pub participant_identity: ::prost::alloc::string::String,
    #[prost(string, tag="6")]
    pub participant_metadata: ::prost::alloc::string::String,
    /// NEXT_ID: 9
    #[prost(map="string, string", tag="7")]
    pub participant_attributes: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateJobStatus {
    #[prost(string, tag="1")]
    pub job_id: ::prost::alloc::string::String,
    /// The worker can indicate the job end by either specifying SUCCESS or FAILED
    #[prost(enumeration="JobStatus", tag="2")]
    pub status: i32,
    /// metadata shown on the dashboard, useful for debugging
    #[prost(string, tag="3")]
    pub error: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateWorkerStatus {
    #[prost(enumeration="WorkerStatus", optional, tag="1")]
    pub status: ::core::option::Option<i32>,
    /// optional string metadata = 2 \[deprecated=true\];
    #[prost(float, tag="3")]
    pub load: f32,
    #[prost(uint32, tag="4")]
    pub job_count: u32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct JobAssignment {
    #[prost(message, optional, tag="1")]
    pub job: ::core::option::Option<Job>,
    #[prost(string, optional, tag="2")]
    pub url: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(string, tag="3")]
    pub token: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct JobTermination {
    #[prost(string, tag="1")]
    pub job_id: ::prost::alloc::string::String,
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum JobType {
    JtRoom = 0,
    JtPublisher = 1,
    JtParticipant = 2,
}
impl JobType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            JobType::JtRoom => "JT_ROOM",
            JobType::JtPublisher => "JT_PUBLISHER",
            JobType::JtParticipant => "JT_PARTICIPANT",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "JT_ROOM" => Some(Self::JtRoom),
            "JT_PUBLISHER" => Some(Self::JtPublisher),
            "JT_PARTICIPANT" => Some(Self::JtParticipant),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum WorkerStatus {
    WsAvailable = 0,
    WsFull = 1,
}
impl WorkerStatus {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            WorkerStatus::WsAvailable => "WS_AVAILABLE",
            WorkerStatus::WsFull => "WS_FULL",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "WS_AVAILABLE" => Some(Self::WsAvailable),
            "WS_FULL" => Some(Self::WsFull),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum JobStatus {
    JsPending = 0,
    JsRunning = 1,
    JsSuccess = 2,
    JsFailed = 3,
}
impl JobStatus {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            JobStatus::JsPending => "JS_PENDING",
            JobStatus::JsRunning => "JS_RUNNING",
            JobStatus::JsSuccess => "JS_SUCCESS",
            JobStatus::JsFailed => "JS_FAILED",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "JS_PENDING" => Some(Self::JsPending),
            "JS_RUNNING" => Some(Self::JsRunning),
            "JS_SUCCESS" => Some(Self::JsSuccess),
            "JS_FAILED" => Some(Self::JsFailed),
            _ => None,
        }
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CreateAgentDispatchRequest {
    #[prost(string, tag="1")]
    pub agent_name: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub room: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub metadata: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RoomAgentDispatch {
    #[prost(string, tag="1")]
    pub agent_name: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub metadata: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DeleteAgentDispatchRequest {
    #[prost(string, tag="1")]
    pub dispatch_id: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub room: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListAgentDispatchRequest {
    /// if set, only the dispatch whose id is given will be returned
    #[prost(string, tag="1")]
    pub dispatch_id: ::prost::alloc::string::String,
    /// name of the room to list agents for. Must be set.
    #[prost(string, tag="2")]
    pub room: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListAgentDispatchResponse {
    #[prost(message, repeated, tag="1")]
    pub agent_dispatches: ::prost::alloc::vec::Vec<AgentDispatch>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AgentDispatch {
    #[prost(string, tag="1")]
    pub id: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub agent_name: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub room: ::prost::alloc::string::String,
    #[prost(string, tag="4")]
    pub metadata: ::prost::alloc::string::String,
    #[prost(message, optional, tag="5")]
    pub state: ::core::option::Option<AgentDispatchState>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct AgentDispatchState {
    /// For dispatches of tyoe JT_ROOM, there will be at most 1 job. 
    /// For dispatches of type JT_PUBLISHER, there will be 1 per publisher.
    #[prost(message, repeated, tag="1")]
    pub jobs: ::prost::alloc::vec::Vec<Job>,
    #[prost(int64, tag="2")]
    pub created_at: i64,
    #[prost(int64, tag="3")]
    pub deleted_at: i64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CreateRoomRequest {
    /// name of the room
    #[prost(string, tag="1")]
    pub name: ::prost::alloc::string::String,
    /// configuration to use for this room parameters. Setting parameters below override the config defaults.
    #[prost(string, tag="12")]
    pub room_preset: ::prost::alloc::string::String,
    /// number of seconds to keep the room open if no one joins
    #[prost(uint32, tag="2")]
    pub empty_timeout: u32,
    /// number of seconds to keep the room open after everyone leaves
    #[prost(uint32, tag="10")]
    pub departure_timeout: u32,
    /// limit number of participants that can be in a room
    #[prost(uint32, tag="3")]
    pub max_participants: u32,
    /// override the node room is allocated to, for debugging
    #[prost(string, tag="4")]
    pub node_id: ::prost::alloc::string::String,
    /// metadata of room
    #[prost(string, tag="5")]
    pub metadata: ::prost::alloc::string::String,
    /// auto-egress configurations
    #[prost(message, optional, tag="6")]
    pub egress: ::core::option::Option<RoomEgress>,
    /// playout delay of subscriber
    #[prost(uint32, tag="7")]
    pub min_playout_delay: u32,
    #[prost(uint32, tag="8")]
    pub max_playout_delay: u32,
    /// improves A/V sync when playout_delay set to a value larger than 200ms. It will disables transceiver re-use
    /// so not recommended for rooms with frequent subscription changes
    #[prost(bool, tag="9")]
    pub sync_streams: bool,
    /// replay
    #[prost(bool, tag="13")]
    pub replay_enabled: bool,
    /// Define agents that should be dispatched to this room
    ///
    /// NEXT-ID: 15
    #[prost(message, repeated, tag="14")]
    pub agents: ::prost::alloc::vec::Vec<RoomAgentDispatch>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RoomEgress {
    #[prost(message, optional, tag="1")]
    pub room: ::core::option::Option<RoomCompositeEgressRequest>,
    #[prost(message, optional, tag="3")]
    pub participant: ::core::option::Option<AutoParticipantEgress>,
    #[prost(message, optional, tag="2")]
    pub tracks: ::core::option::Option<AutoTrackEgress>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RoomAgent {
    #[prost(message, repeated, tag="1")]
    pub dispatches: ::prost::alloc::vec::Vec<RoomAgentDispatch>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListRoomsRequest {
    /// when set, will only return rooms with name match
    #[prost(string, repeated, tag="1")]
    pub names: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListRoomsResponse {
    #[prost(message, repeated, tag="1")]
    pub rooms: ::prost::alloc::vec::Vec<Room>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DeleteRoomRequest {
    /// name of the room
    #[prost(string, tag="1")]
    pub room: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DeleteRoomResponse {
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListParticipantsRequest {
    /// name of the room
    #[prost(string, tag="1")]
    pub room: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListParticipantsResponse {
    #[prost(message, repeated, tag="1")]
    pub participants: ::prost::alloc::vec::Vec<ParticipantInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RoomParticipantIdentity {
    /// name of the room
    #[prost(string, tag="1")]
    pub room: ::prost::alloc::string::String,
    /// identity of the participant
    #[prost(string, tag="2")]
    pub identity: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RemoveParticipantResponse {
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct MuteRoomTrackRequest {
    /// name of the room
    #[prost(string, tag="1")]
    pub room: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub identity: ::prost::alloc::string::String,
    /// sid of the track to mute
    #[prost(string, tag="3")]
    pub track_sid: ::prost::alloc::string::String,
    /// set to true to mute, false to unmute
    #[prost(bool, tag="4")]
    pub muted: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct MuteRoomTrackResponse {
    #[prost(message, optional, tag="1")]
    pub track: ::core::option::Option<TrackInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateParticipantRequest {
    #[prost(string, tag="1")]
    pub room: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub identity: ::prost::alloc::string::String,
    /// metadata to update. skipping updates if left empty
    #[prost(string, tag="3")]
    pub metadata: ::prost::alloc::string::String,
    /// set to update the participant's permissions
    #[prost(message, optional, tag="4")]
    pub permission: ::core::option::Option<ParticipantPermission>,
    /// display name to update
    #[prost(string, tag="5")]
    pub name: ::prost::alloc::string::String,
    /// attributes to update. it only updates attributes that have been set
    /// to delete attributes, set the value to an empty string
    #[prost(map="string, string", tag="6")]
    pub attributes: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateSubscriptionsRequest {
    #[prost(string, tag="1")]
    pub room: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub identity: ::prost::alloc::string::String,
    /// list of sids of tracks
    #[prost(string, repeated, tag="3")]
    pub track_sids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// set to true to subscribe, false to unsubscribe from tracks
    #[prost(bool, tag="4")]
    pub subscribe: bool,
    /// list of participants and their tracks
    #[prost(message, repeated, tag="5")]
    pub participant_tracks: ::prost::alloc::vec::Vec<ParticipantTracks>,
}
/// empty for now
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateSubscriptionsResponse {
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SendDataRequest {
    #[prost(string, tag="1")]
    pub room: ::prost::alloc::string::String,
    #[prost(bytes="vec", tag="2")]
    pub data: ::prost::alloc::vec::Vec<u8>,
    #[prost(enumeration="data_packet::Kind", tag="3")]
    pub kind: i32,
    /// mark deprecated
    #[deprecated]
    #[prost(string, repeated, tag="4")]
    pub destination_sids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// when set, only forward to these identities
    #[prost(string, repeated, tag="6")]
    pub destination_identities: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(string, optional, tag="5")]
    pub topic: ::core::option::Option<::prost::alloc::string::String>,
    /// added by SDK to enable de-duping of messages, for INTERNAL USE ONLY
    #[prost(bytes="vec", tag="7")]
    pub nonce: ::prost::alloc::vec::Vec<u8>,
}
///
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SendDataResponse {
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateRoomMetadataRequest {
    #[prost(string, tag="1")]
    pub room: ::prost::alloc::string::String,
    /// metadata to update. skipping updates if left empty
    #[prost(string, tag="2")]
    pub metadata: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct RoomConfiguration {
    /// Used as ID, must be unique
    #[prost(string, tag="1")]
    pub name: ::prost::alloc::string::String,
    /// number of seconds to keep the room open if no one joins
    #[prost(uint32, tag="2")]
    pub empty_timeout: u32,
    /// number of seconds to keep the room open after everyone leaves
    #[prost(uint32, tag="3")]
    pub departure_timeout: u32,
    /// limit number of participants that can be in a room, excluding Egress and Ingress participants
    #[prost(uint32, tag="4")]
    pub max_participants: u32,
    /// egress
    #[prost(message, optional, tag="5")]
    pub egress: ::core::option::Option<RoomEgress>,
    /// playout delay of subscriber
    #[prost(uint32, tag="7")]
    pub min_playout_delay: u32,
    #[prost(uint32, tag="8")]
    pub max_playout_delay: u32,
    /// improves A/V sync when playout_delay set to a value larger than 200ms. It will disables transceiver re-use
    /// so not recommended for rooms with frequent subscription changes
    #[prost(bool, tag="9")]
    pub sync_streams: bool,
    /// Define agents that should be dispatched to this room
    #[prost(message, repeated, tag="10")]
    pub agents: ::prost::alloc::vec::Vec<RoomAgentDispatch>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ForwardParticipantRequest {
    /// room to forward participant from
    #[prost(string, tag="1")]
    pub room: ::prost::alloc::string::String,
    /// identity of the participant to forward
    #[prost(string, tag="2")]
    pub identity: ::prost::alloc::string::String,
    /// room to forward participant to
    #[prost(string, tag="3")]
    pub destination_room: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ForwardParticipantResponse {
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct MoveParticipantRequest {
    /// room to move participant from
    #[prost(string, tag="1")]
    pub room: ::prost::alloc::string::String,
    /// identity of the participant to move to
    #[prost(string, tag="2")]
    pub identity: ::prost::alloc::string::String,
    /// room to move participant to
    #[prost(string, tag="3")]
    pub destination_room: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct MoveParticipantResponse {
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CreateIngressRequest {
    #[prost(enumeration="IngressInput", tag="1")]
    pub input_type: i32,
    /// Where to pull media from, only for URL input type
    #[prost(string, tag="9")]
    pub url: ::prost::alloc::string::String,
    /// User provided identifier for the ingress
    #[prost(string, tag="2")]
    pub name: ::prost::alloc::string::String,
    /// room to publish to
    #[prost(string, tag="3")]
    pub room_name: ::prost::alloc::string::String,
    /// publish as participant
    #[prost(string, tag="4")]
    pub participant_identity: ::prost::alloc::string::String,
    /// name of publishing participant (used for display only)
    #[prost(string, tag="5")]
    pub participant_name: ::prost::alloc::string::String,
    /// metadata associated with the publishing participant
    #[prost(string, tag="10")]
    pub participant_metadata: ::prost::alloc::string::String,
    /// \[depreacted \] whether to pass through the incoming media without transcoding, only compatible with some input types. Use `enable_transcoding` instead.
    #[deprecated]
    #[prost(bool, tag="8")]
    pub bypass_transcoding: bool,
    /// Whether to transcode the ingested media. Only WHIP supports disabling transcoding currently. WHIP will default to transcoding disabled. Replaces `bypass_transcoding. 
    #[prost(bool, optional, tag="11")]
    pub enable_transcoding: ::core::option::Option<bool>,
    #[prost(message, optional, tag="6")]
    pub audio: ::core::option::Option<IngressAudioOptions>,
    #[prost(message, optional, tag="7")]
    pub video: ::core::option::Option<IngressVideoOptions>,
    /// The default value is true and when set to false, the new connection attempts will be rejected
    #[prost(bool, optional, tag="12")]
    pub enabled: ::core::option::Option<bool>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct IngressAudioOptions {
    #[prost(string, tag="1")]
    pub name: ::prost::alloc::string::String,
    #[prost(enumeration="TrackSource", tag="2")]
    pub source: i32,
    #[prost(oneof="ingress_audio_options::EncodingOptions", tags="3, 4")]
    pub encoding_options: ::core::option::Option<ingress_audio_options::EncodingOptions>,
}
/// Nested message and enum types in `IngressAudioOptions`.
pub mod ingress_audio_options {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum EncodingOptions {
        #[prost(enumeration="super::IngressAudioEncodingPreset", tag="3")]
        Preset(i32),
        #[prost(message, tag="4")]
        Options(super::IngressAudioEncodingOptions),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct IngressVideoOptions {
    #[prost(string, tag="1")]
    pub name: ::prost::alloc::string::String,
    #[prost(enumeration="TrackSource", tag="2")]
    pub source: i32,
    #[prost(oneof="ingress_video_options::EncodingOptions", tags="3, 4")]
    pub encoding_options: ::core::option::Option<ingress_video_options::EncodingOptions>,
}
/// Nested message and enum types in `IngressVideoOptions`.
pub mod ingress_video_options {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum EncodingOptions {
        #[prost(enumeration="super::IngressVideoEncodingPreset", tag="3")]
        Preset(i32),
        #[prost(message, tag="4")]
        Options(super::IngressVideoEncodingOptions),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct IngressAudioEncodingOptions {
    /// desired audio codec to publish to room
    #[prost(enumeration="AudioCodec", tag="1")]
    pub audio_codec: i32,
    #[prost(uint32, tag="2")]
    pub bitrate: u32,
    #[prost(bool, tag="3")]
    pub disable_dtx: bool,
    #[prost(uint32, tag="4")]
    pub channels: u32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct IngressVideoEncodingOptions {
    /// desired codec to publish to room
    #[prost(enumeration="VideoCodec", tag="1")]
    pub video_codec: i32,
    #[prost(double, tag="2")]
    pub frame_rate: f64,
    /// simulcast layers to publish, when empty, should usually be set to layers at 1/2 and 1/4 of the dimensions
    #[prost(message, repeated, tag="3")]
    pub layers: ::prost::alloc::vec::Vec<VideoLayer>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct IngressInfo {
    #[prost(string, tag="1")]
    pub ingress_id: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub stream_key: ::prost::alloc::string::String,
    /// URL to point the encoder to for push (RTMP, WHIP), or location to pull media from for pull (URL)
    #[prost(string, tag="4")]
    pub url: ::prost::alloc::string::String,
    /// for RTMP input, it'll be a rtmp:// URL
    /// for FILE input, it'll be a http:// URL
    /// for SRT input, it'll be a srt:// URL
    #[prost(enumeration="IngressInput", tag="5")]
    pub input_type: i32,
    #[deprecated]
    #[prost(bool, tag="13")]
    pub bypass_transcoding: bool,
    #[prost(bool, optional, tag="15")]
    pub enable_transcoding: ::core::option::Option<bool>,
    #[prost(message, optional, tag="6")]
    pub audio: ::core::option::Option<IngressAudioOptions>,
    #[prost(message, optional, tag="7")]
    pub video: ::core::option::Option<IngressVideoOptions>,
    #[prost(string, tag="8")]
    pub room_name: ::prost::alloc::string::String,
    #[prost(string, tag="9")]
    pub participant_identity: ::prost::alloc::string::String,
    #[prost(string, tag="10")]
    pub participant_name: ::prost::alloc::string::String,
    #[prost(string, tag="14")]
    pub participant_metadata: ::prost::alloc::string::String,
    #[prost(bool, tag="11")]
    pub reusable: bool,
    /// Description of error/stream non compliance and debug info for publisher otherwise (received bitrate, resolution, bandwidth)
    #[prost(message, optional, tag="12")]
    pub state: ::core::option::Option<IngressState>,
    /// The default value is true and when set to false, the new connection attempts will be rejected
    #[prost(bool, optional, tag="16")]
    pub enabled: ::core::option::Option<bool>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct IngressState {
    #[prost(enumeration="ingress_state::Status", tag="1")]
    pub status: i32,
    /// Error/non compliance description if any
    #[prost(string, tag="2")]
    pub error: ::prost::alloc::string::String,
    #[prost(message, optional, tag="3")]
    pub video: ::core::option::Option<InputVideoState>,
    #[prost(message, optional, tag="4")]
    pub audio: ::core::option::Option<InputAudioState>,
    /// ID of the current/previous room published to
    #[prost(string, tag="5")]
    pub room_id: ::prost::alloc::string::String,
    #[prost(int64, tag="7")]
    pub started_at: i64,
    #[prost(int64, tag="8")]
    pub ended_at: i64,
    #[prost(int64, tag="10")]
    pub updated_at: i64,
    #[prost(string, tag="9")]
    pub resource_id: ::prost::alloc::string::String,
    #[prost(message, repeated, tag="6")]
    pub tracks: ::prost::alloc::vec::Vec<TrackInfo>,
}
/// Nested message and enum types in `IngressState`.
pub mod ingress_state {
    #[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
    #[repr(i32)]
    pub enum Status {
        EndpointInactive = 0,
        EndpointBuffering = 1,
        EndpointPublishing = 2,
        EndpointError = 3,
        EndpointComplete = 4,
    }
    impl Status {
        /// String value of the enum field names used in the ProtoBuf definition.
        ///
        /// The values are not transformed in any way and thus are considered stable
        /// (if the ProtoBuf definition does not change) and safe for programmatic use.
        pub fn as_str_name(&self) -> &'static str {
            match self {
                Status::EndpointInactive => "ENDPOINT_INACTIVE",
                Status::EndpointBuffering => "ENDPOINT_BUFFERING",
                Status::EndpointPublishing => "ENDPOINT_PUBLISHING",
                Status::EndpointError => "ENDPOINT_ERROR",
                Status::EndpointComplete => "ENDPOINT_COMPLETE",
            }
        }
        /// Creates an enum from field names used in the ProtoBuf definition.
        pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
            match value {
                "ENDPOINT_INACTIVE" => Some(Self::EndpointInactive),
                "ENDPOINT_BUFFERING" => Some(Self::EndpointBuffering),
                "ENDPOINT_PUBLISHING" => Some(Self::EndpointPublishing),
                "ENDPOINT_ERROR" => Some(Self::EndpointError),
                "ENDPOINT_COMPLETE" => Some(Self::EndpointComplete),
                _ => None,
            }
        }
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct InputVideoState {
    #[prost(string, tag="1")]
    pub mime_type: ::prost::alloc::string::String,
    #[prost(uint32, tag="2")]
    pub average_bitrate: u32,
    #[prost(uint32, tag="3")]
    pub width: u32,
    #[prost(uint32, tag="4")]
    pub height: u32,
    #[prost(double, tag="5")]
    pub framerate: f64,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct InputAudioState {
    #[prost(string, tag="1")]
    pub mime_type: ::prost::alloc::string::String,
    #[prost(uint32, tag="2")]
    pub average_bitrate: u32,
    #[prost(uint32, tag="3")]
    pub channels: u32,
    #[prost(uint32, tag="4")]
    pub sample_rate: u32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateIngressRequest {
    #[prost(string, tag="1")]
    pub ingress_id: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub name: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub room_name: ::prost::alloc::string::String,
    #[prost(string, tag="4")]
    pub participant_identity: ::prost::alloc::string::String,
    #[prost(string, tag="5")]
    pub participant_name: ::prost::alloc::string::String,
    #[prost(string, tag="9")]
    pub participant_metadata: ::prost::alloc::string::String,
    #[deprecated]
    #[prost(bool, optional, tag="8")]
    pub bypass_transcoding: ::core::option::Option<bool>,
    #[prost(bool, optional, tag="10")]
    pub enable_transcoding: ::core::option::Option<bool>,
    #[prost(message, optional, tag="6")]
    pub audio: ::core::option::Option<IngressAudioOptions>,
    #[prost(message, optional, tag="7")]
    pub video: ::core::option::Option<IngressVideoOptions>,
    /// The default value is true and when set to false, the new connection attempts will be rejected
    #[prost(bool, optional, tag="11")]
    pub enabled: ::core::option::Option<bool>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListIngressRequest {
    /// when blank, lists all ingress endpoints
    ///
    /// (optional, filter by room name)
    #[prost(string, tag="1")]
    pub room_name: ::prost::alloc::string::String,
    /// (optional, filter by ingress ID)
    #[prost(string, tag="2")]
    pub ingress_id: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListIngressResponse {
    #[prost(message, repeated, tag="1")]
    pub items: ::prost::alloc::vec::Vec<IngressInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DeleteIngressRequest {
    #[prost(string, tag="1")]
    pub ingress_id: ::prost::alloc::string::String,
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum IngressInput {
    RtmpInput = 0,
    WhipInput = 1,
    /// Pull from the provided URL. Only HTTP url are supported, serving either a single media file or a HLS stream
    UrlInput = 2,
}
impl IngressInput {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            IngressInput::RtmpInput => "RTMP_INPUT",
            IngressInput::WhipInput => "WHIP_INPUT",
            IngressInput::UrlInput => "URL_INPUT",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "RTMP_INPUT" => Some(Self::RtmpInput),
            "WHIP_INPUT" => Some(Self::WhipInput),
            "URL_INPUT" => Some(Self::UrlInput),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum IngressAudioEncodingPreset {
    /// OPUS, 2 channels, 96kbps
    OpusStereo96kbps = 0,
    /// OPUS, 1 channel, 64kbps
    OpusMono64kbs = 1,
}
impl IngressAudioEncodingPreset {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            IngressAudioEncodingPreset::OpusStereo96kbps => "OPUS_STEREO_96KBPS",
            IngressAudioEncodingPreset::OpusMono64kbs => "OPUS_MONO_64KBS",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "OPUS_STEREO_96KBPS" => Some(Self::OpusStereo96kbps),
            "OPUS_MONO_64KBS" => Some(Self::OpusMono64kbs),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum IngressVideoEncodingPreset {
    /// 1280x720,  30fps, 1900kbps main layer, 3 layers total
    H264720p30fps3Layers = 0,
    /// 1980x1080, 30fps, 3500kbps main layer, 3 layers total
    H2641080p30fps3Layers = 1,
    ///   960x540,  25fps, 1000kbps  main layer, 2 layers total
    H264540p25fps2Layers = 2,
    /// 1280x720,  30fps, 1900kbps, no simulcast
    H264720p30fps1Layer = 3,
    /// 1980x1080, 30fps, 3500kbps, no simulcast
    H2641080p30fps1Layer = 4,
    /// 1280x720,  30fps, 2500kbps main layer, 3 layers total, higher bitrate for high motion, harder to encode content
    H264720p30fps3LayersHighMotion = 5,
    /// 1980x1080, 30fps, 4500kbps main layer, 3 layers total, higher bitrate for high motion, harder to encode content
    H2641080p30fps3LayersHighMotion = 6,
    ///   960x540,  25fps, 1300kbps  main layer, 2 layers total, higher bitrate for high motion, harder to encode content
    H264540p25fps2LayersHighMotion = 7,
    /// 1280x720,  30fps, 2500kbps, no simulcast, higher bitrate for high motion, harder to encode content
    H264720p30fps1LayerHighMotion = 8,
    /// 1980x1080, 30fps, 4500kbps, no simulcast, higher bitrate for high motion, harder to encode content
    H2641080p30fps1LayerHighMotion = 9,
}
impl IngressVideoEncodingPreset {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            IngressVideoEncodingPreset::H264720p30fps3Layers => "H264_720P_30FPS_3_LAYERS",
            IngressVideoEncodingPreset::H2641080p30fps3Layers => "H264_1080P_30FPS_3_LAYERS",
            IngressVideoEncodingPreset::H264540p25fps2Layers => "H264_540P_25FPS_2_LAYERS",
            IngressVideoEncodingPreset::H264720p30fps1Layer => "H264_720P_30FPS_1_LAYER",
            IngressVideoEncodingPreset::H2641080p30fps1Layer => "H264_1080P_30FPS_1_LAYER",
            IngressVideoEncodingPreset::H264720p30fps3LayersHighMotion => "H264_720P_30FPS_3_LAYERS_HIGH_MOTION",
            IngressVideoEncodingPreset::H2641080p30fps3LayersHighMotion => "H264_1080P_30FPS_3_LAYERS_HIGH_MOTION",
            IngressVideoEncodingPreset::H264540p25fps2LayersHighMotion => "H264_540P_25FPS_2_LAYERS_HIGH_MOTION",
            IngressVideoEncodingPreset::H264720p30fps1LayerHighMotion => "H264_720P_30FPS_1_LAYER_HIGH_MOTION",
            IngressVideoEncodingPreset::H2641080p30fps1LayerHighMotion => "H264_1080P_30FPS_1_LAYER_HIGH_MOTION",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "H264_720P_30FPS_3_LAYERS" => Some(Self::H264720p30fps3Layers),
            "H264_1080P_30FPS_3_LAYERS" => Some(Self::H2641080p30fps3Layers),
            "H264_540P_25FPS_2_LAYERS" => Some(Self::H264540p25fps2Layers),
            "H264_720P_30FPS_1_LAYER" => Some(Self::H264720p30fps1Layer),
            "H264_1080P_30FPS_1_LAYER" => Some(Self::H2641080p30fps1Layer),
            "H264_720P_30FPS_3_LAYERS_HIGH_MOTION" => Some(Self::H264720p30fps3LayersHighMotion),
            "H264_1080P_30FPS_3_LAYERS_HIGH_MOTION" => Some(Self::H2641080p30fps3LayersHighMotion),
            "H264_540P_25FPS_2_LAYERS_HIGH_MOTION" => Some(Self::H264540p25fps2LayersHighMotion),
            "H264_720P_30FPS_1_LAYER_HIGH_MOTION" => Some(Self::H264720p30fps1LayerHighMotion),
            "H264_1080P_30FPS_1_LAYER_HIGH_MOTION" => Some(Self::H2641080p30fps1LayerHighMotion),
            _ => None,
        }
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct WebhookEvent {
    /// one of room_started, room_finished, participant_joined, participant_left,
    /// track_published, track_unpublished, egress_started, egress_updated, egress_ended,
    /// ingress_started, ingress_ended
    #[prost(string, tag="1")]
    pub event: ::prost::alloc::string::String,
    #[prost(message, optional, tag="2")]
    pub room: ::core::option::Option<Room>,
    /// set when event is participant_* or track_*
    #[prost(message, optional, tag="3")]
    pub participant: ::core::option::Option<ParticipantInfo>,
    /// set when event is egress_*
    #[prost(message, optional, tag="9")]
    pub egress_info: ::core::option::Option<EgressInfo>,
    /// set when event is ingress_*
    #[prost(message, optional, tag="10")]
    pub ingress_info: ::core::option::Option<IngressInfo>,
    /// set when event is track_*
    #[prost(message, optional, tag="8")]
    pub track: ::core::option::Option<TrackInfo>,
    /// unique event uuid
    #[prost(string, tag="6")]
    pub id: ::prost::alloc::string::String,
    /// timestamp in seconds
    #[prost(int64, tag="7")]
    pub created_at: i64,
    #[deprecated]
    #[prost(int32, tag="11")]
    pub num_dropped: i32,
}
/// SIPStatus is returned as an error detail in CreateSIPParticipant.
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipStatus {
    #[prost(enumeration="SipStatusCode", tag="1")]
    pub code: i32,
    #[prost(string, tag="2")]
    pub status: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CreateSipTrunkRequest {
    /// CIDR or IPs that traffic is accepted from
    /// An empty list means all inbound traffic is accepted.
    #[prost(string, repeated, tag="1")]
    pub inbound_addresses: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// IP that SIP INVITE is sent too
    #[prost(string, tag="2")]
    pub outbound_address: ::prost::alloc::string::String,
    /// Number used to make outbound calls
    #[prost(string, tag="3")]
    pub outbound_number: ::prost::alloc::string::String,
    #[deprecated]
    #[prost(string, repeated, tag="4")]
    pub inbound_numbers_regex: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// Accepted `To` values. This Trunk will only accept a call made to
    /// these numbers. This allows you to have distinct Trunks for different phone
    /// numbers at the same provider.
    #[prost(string, repeated, tag="9")]
    pub inbound_numbers: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// Username and password used to authenticate inbound and outbound SIP invites
    /// May be empty to have no Authentication
    #[prost(string, tag="5")]
    pub inbound_username: ::prost::alloc::string::String,
    #[prost(string, tag="6")]
    pub inbound_password: ::prost::alloc::string::String,
    #[prost(string, tag="7")]
    pub outbound_username: ::prost::alloc::string::String,
    #[prost(string, tag="8")]
    pub outbound_password: ::prost::alloc::string::String,
    /// Optional human-readable name for the Trunk.
    #[prost(string, tag="10")]
    pub name: ::prost::alloc::string::String,
    /// Optional user-defined metadata for the Trunk.
    #[prost(string, tag="11")]
    pub metadata: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipTrunkInfo {
    #[prost(string, tag="1")]
    pub sip_trunk_id: ::prost::alloc::string::String,
    #[prost(enumeration="sip_trunk_info::TrunkKind", tag="14")]
    pub kind: i32,
    /// CIDR or IPs that traffic is accepted from
    /// An empty list means all inbound traffic is accepted.
    #[prost(string, repeated, tag="2")]
    pub inbound_addresses: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// IP that SIP INVITE is sent too
    #[prost(string, tag="3")]
    pub outbound_address: ::prost::alloc::string::String,
    /// Number used to make outbound calls
    #[prost(string, tag="4")]
    pub outbound_number: ::prost::alloc::string::String,
    /// Transport used for inbound and outbound calls.
    #[prost(enumeration="SipTransport", tag="13")]
    pub transport: i32,
    #[deprecated]
    #[prost(string, repeated, tag="5")]
    pub inbound_numbers_regex: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// Accepted `To` values. This Trunk will only accept a call made to
    /// these numbers. This allows you to have distinct Trunks for different phone
    /// numbers at the same provider.
    #[prost(string, repeated, tag="10")]
    pub inbound_numbers: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// Username and password used to authenticate inbound and outbound SIP invites
    /// May be empty to have no Authentication
    #[prost(string, tag="6")]
    pub inbound_username: ::prost::alloc::string::String,
    #[prost(string, tag="7")]
    pub inbound_password: ::prost::alloc::string::String,
    #[prost(string, tag="8")]
    pub outbound_username: ::prost::alloc::string::String,
    #[prost(string, tag="9")]
    pub outbound_password: ::prost::alloc::string::String,
    /// Human-readable name for the Trunk.
    #[prost(string, tag="11")]
    pub name: ::prost::alloc::string::String,
    /// User-defined metadata for the Trunk.
    #[prost(string, tag="12")]
    pub metadata: ::prost::alloc::string::String,
}
/// Nested message and enum types in `SIPTrunkInfo`.
pub mod sip_trunk_info {
    #[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
    #[repr(i32)]
    pub enum TrunkKind {
        TrunkLegacy = 0,
        TrunkInbound = 1,
        TrunkOutbound = 2,
    }
    impl TrunkKind {
        /// String value of the enum field names used in the ProtoBuf definition.
        ///
        /// The values are not transformed in any way and thus are considered stable
        /// (if the ProtoBuf definition does not change) and safe for programmatic use.
        pub fn as_str_name(&self) -> &'static str {
            match self {
                TrunkKind::TrunkLegacy => "TRUNK_LEGACY",
                TrunkKind::TrunkInbound => "TRUNK_INBOUND",
                TrunkKind::TrunkOutbound => "TRUNK_OUTBOUND",
            }
        }
        /// Creates an enum from field names used in the ProtoBuf definition.
        pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
            match value {
                "TRUNK_LEGACY" => Some(Self::TrunkLegacy),
                "TRUNK_INBOUND" => Some(Self::TrunkInbound),
                "TRUNK_OUTBOUND" => Some(Self::TrunkOutbound),
                _ => None,
            }
        }
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CreateSipInboundTrunkRequest {
    /// Trunk ID is ignored
    #[prost(message, optional, tag="1")]
    pub trunk: ::core::option::Option<SipInboundTrunkInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateSipInboundTrunkRequest {
    #[prost(string, tag="1")]
    pub sip_trunk_id: ::prost::alloc::string::String,
    #[prost(oneof="update_sip_inbound_trunk_request::Action", tags="2, 3")]
    pub action: ::core::option::Option<update_sip_inbound_trunk_request::Action>,
}
/// Nested message and enum types in `UpdateSIPInboundTrunkRequest`.
pub mod update_sip_inbound_trunk_request {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Action {
        #[prost(message, tag="2")]
        Replace(super::SipInboundTrunkInfo),
        #[prost(message, tag="3")]
        Update(super::SipInboundTrunkUpdate),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipInboundTrunkInfo {
    #[prost(string, tag="1")]
    pub sip_trunk_id: ::prost::alloc::string::String,
    /// Human-readable name for the Trunk.
    #[prost(string, tag="2")]
    pub name: ::prost::alloc::string::String,
    /// User-defined metadata for the Trunk.
    #[prost(string, tag="3")]
    pub metadata: ::prost::alloc::string::String,
    /// Numbers associated with LiveKit SIP. The Trunk will only accept calls made to these numbers.
    /// Creating multiple Trunks with different phone numbers allows having different rules for a single provider.
    #[prost(string, repeated, tag="4")]
    pub numbers: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// CIDR or IPs that traffic is accepted from.
    /// An empty list means all inbound traffic is accepted.
    #[prost(string, repeated, tag="5")]
    pub allowed_addresses: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// Numbers that are allowed to make calls to this Trunk.
    /// An empty list means calls from any phone number is accepted.
    #[prost(string, repeated, tag="6")]
    pub allowed_numbers: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// Username and password used to authenticate inbound SIP invites.
    /// May be empty to have no authentication.
    #[prost(string, tag="7")]
    pub auth_username: ::prost::alloc::string::String,
    #[prost(string, tag="8")]
    pub auth_password: ::prost::alloc::string::String,
    /// Include these SIP X-* headers in 200 OK responses.
    #[prost(map="string, string", tag="9")]
    pub headers: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    /// Map SIP X-* headers from INVITE to SIP participant attributes.
    #[prost(map="string, string", tag="10")]
    pub headers_to_attributes: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    /// Map LiveKit attributes to SIP X-* headers when sending BYE or REFER requests.
    /// Keys are the names of attributes and values are the names of X-* headers they will be mapped to.
    #[prost(map="string, string", tag="14")]
    pub attributes_to_headers: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    /// Map SIP headers from INVITE to sip.h.* participant attributes automatically.
    ///
    /// When the names of required headers is known, using headers_to_attributes is strongly recommended.
    ///
    /// When mapping INVITE headers to response headers with attributes_to_headers map,
    /// lowercase header names should be used, for example: sip.h.x-custom-header.
    #[prost(enumeration="SipHeaderOptions", tag="15")]
    pub include_headers: i32,
    /// Max time for the caller to wait for track subscription.
    #[prost(message, optional, tag="11")]
    pub ringing_timeout: ::core::option::Option<::pbjson_types::Duration>,
    /// Max call duration.
    #[prost(message, optional, tag="12")]
    pub max_call_duration: ::core::option::Option<::pbjson_types::Duration>,
    #[prost(bool, tag="13")]
    pub krisp_enabled: bool,
    #[prost(enumeration="SipMediaEncryption", tag="16")]
    pub media_encryption: i32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipInboundTrunkUpdate {
    #[prost(message, optional, tag="1")]
    pub numbers: ::core::option::Option<ListUpdate>,
    #[prost(message, optional, tag="2")]
    pub allowed_addresses: ::core::option::Option<ListUpdate>,
    #[prost(message, optional, tag="3")]
    pub allowed_numbers: ::core::option::Option<ListUpdate>,
    #[prost(string, optional, tag="4")]
    pub auth_username: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(string, optional, tag="5")]
    pub auth_password: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(string, optional, tag="6")]
    pub name: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(string, optional, tag="7")]
    pub metadata: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(enumeration="SipMediaEncryption", optional, tag="8")]
    pub media_encryption: ::core::option::Option<i32>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CreateSipOutboundTrunkRequest {
    /// Trunk ID is ignored
    #[prost(message, optional, tag="1")]
    pub trunk: ::core::option::Option<SipOutboundTrunkInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateSipOutboundTrunkRequest {
    #[prost(string, tag="1")]
    pub sip_trunk_id: ::prost::alloc::string::String,
    #[prost(oneof="update_sip_outbound_trunk_request::Action", tags="2, 3")]
    pub action: ::core::option::Option<update_sip_outbound_trunk_request::Action>,
}
/// Nested message and enum types in `UpdateSIPOutboundTrunkRequest`.
pub mod update_sip_outbound_trunk_request {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Action {
        #[prost(message, tag="2")]
        Replace(super::SipOutboundTrunkInfo),
        #[prost(message, tag="3")]
        Update(super::SipOutboundTrunkUpdate),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipOutboundTrunkInfo {
    #[prost(string, tag="1")]
    pub sip_trunk_id: ::prost::alloc::string::String,
    /// Human-readable name for the Trunk.
    #[prost(string, tag="2")]
    pub name: ::prost::alloc::string::String,
    /// User-defined metadata for the Trunk.
    #[prost(string, tag="3")]
    pub metadata: ::prost::alloc::string::String,
    /// Hostname or IP that SIP INVITE is sent too.
    /// Note that this is not a SIP URI and should not contain the 'sip:' protocol prefix.
    #[prost(string, tag="4")]
    pub address: ::prost::alloc::string::String,
    /// country where the call terminates as ISO 3166-1 alpha-2 (<https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2>). This will be used by the livekit infrastructure to route calls.
    #[prost(string, tag="14")]
    pub destination_country: ::prost::alloc::string::String,
    /// SIP Transport used for outbound call.
    #[prost(enumeration="SipTransport", tag="5")]
    pub transport: i32,
    /// Numbers used to make the calls. Random one from this list will be selected.
    #[prost(string, repeated, tag="6")]
    pub numbers: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// Username and password used to authenticate with SIP server.
    /// May be empty to have no authentication.
    #[prost(string, tag="7")]
    pub auth_username: ::prost::alloc::string::String,
    #[prost(string, tag="8")]
    pub auth_password: ::prost::alloc::string::String,
    /// Include these SIP X-* headers in INVITE request.
    /// These headers are sent as-is and may help identify this call as coming from LiveKit for the other SIP endpoint.
    #[prost(map="string, string", tag="9")]
    pub headers: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    /// Map SIP X-* headers from 200 OK to SIP participant attributes.
    /// Keys are the names of X-* headers and values are the names of attributes they will be mapped to.
    #[prost(map="string, string", tag="10")]
    pub headers_to_attributes: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    /// Map LiveKit attributes to SIP X-* headers when sending BYE or REFER requests.
    /// Keys are the names of attributes and values are the names of X-* headers they will be mapped to.
    #[prost(map="string, string", tag="11")]
    pub attributes_to_headers: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    /// Map SIP headers from 200 OK to sip.h.* participant attributes automatically.
    ///
    /// When the names of required headers is known, using headers_to_attributes is strongly recommended.
    ///
    /// When mapping 200 OK headers to follow-up request headers with attributes_to_headers map,
    /// lowercase header names should be used, for example: sip.h.x-custom-header.
    #[prost(enumeration="SipHeaderOptions", tag="12")]
    pub include_headers: i32,
    #[prost(enumeration="SipMediaEncryption", tag="13")]
    pub media_encryption: i32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipOutboundTrunkUpdate {
    #[prost(string, optional, tag="1")]
    pub address: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(enumeration="SipTransport", optional, tag="2")]
    pub transport: ::core::option::Option<i32>,
    #[prost(string, optional, tag="9")]
    pub destination_country: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(message, optional, tag="3")]
    pub numbers: ::core::option::Option<ListUpdate>,
    #[prost(string, optional, tag="4")]
    pub auth_username: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(string, optional, tag="5")]
    pub auth_password: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(string, optional, tag="6")]
    pub name: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(string, optional, tag="7")]
    pub metadata: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(enumeration="SipMediaEncryption", optional, tag="8")]
    pub media_encryption: ::core::option::Option<i32>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetSipInboundTrunkRequest {
    #[prost(string, tag="1")]
    pub sip_trunk_id: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetSipInboundTrunkResponse {
    #[prost(message, optional, tag="1")]
    pub trunk: ::core::option::Option<SipInboundTrunkInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetSipOutboundTrunkRequest {
    #[prost(string, tag="1")]
    pub sip_trunk_id: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct GetSipOutboundTrunkResponse {
    #[prost(message, optional, tag="1")]
    pub trunk: ::core::option::Option<SipOutboundTrunkInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListSipTrunkRequest {
    #[prost(message, optional, tag="1")]
    pub page: ::core::option::Option<Pagination>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListSipTrunkResponse {
    #[prost(message, repeated, tag="1")]
    pub items: ::prost::alloc::vec::Vec<SipTrunkInfo>,
}
/// ListSIPInboundTrunkRequest lists inbound trunks for given filters. If no filters are set, all trunks are listed.
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListSipInboundTrunkRequest {
    #[prost(message, optional, tag="3")]
    pub page: ::core::option::Option<Pagination>,
    /// Trunk IDs to list. If this option is set, the response will contains trunks in the same order.
    /// If any of the trunks is missing, a nil item in that position will be sent in the response.
    #[prost(string, repeated, tag="1")]
    pub trunk_ids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// Only list trunks that contain one of the numbers, including wildcard trunks.
    #[prost(string, repeated, tag="2")]
    pub numbers: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListSipInboundTrunkResponse {
    #[prost(message, repeated, tag="1")]
    pub items: ::prost::alloc::vec::Vec<SipInboundTrunkInfo>,
}
/// ListSIPOutboundTrunkRequest lists outbound trunks for given filters. If no filters are set, all trunks are listed.
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListSipOutboundTrunkRequest {
    #[prost(message, optional, tag="3")]
    pub page: ::core::option::Option<Pagination>,
    /// Trunk IDs to list. If this option is set, the response will contains trunks in the same order.
    /// If any of the trunks is missing, a nil item in that position will be sent in the response.
    #[prost(string, repeated, tag="1")]
    pub trunk_ids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// Only list trunks that contain one of the numbers, including wildcard trunks.
    #[prost(string, repeated, tag="2")]
    pub numbers: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListSipOutboundTrunkResponse {
    #[prost(message, repeated, tag="1")]
    pub items: ::prost::alloc::vec::Vec<SipOutboundTrunkInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DeleteSipTrunkRequest {
    #[prost(string, tag="1")]
    pub sip_trunk_id: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipDispatchRuleDirect {
    /// What room should call be directed into
    #[prost(string, tag="1")]
    pub room_name: ::prost::alloc::string::String,
    /// Optional pin required to enter room
    #[prost(string, tag="2")]
    pub pin: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipDispatchRuleIndividual {
    /// Prefix used on new room name
    #[prost(string, tag="1")]
    pub room_prefix: ::prost::alloc::string::String,
    /// Optional pin required to enter room
    #[prost(string, tag="2")]
    pub pin: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipDispatchRuleCallee {
    /// Prefix used on new room name
    #[prost(string, tag="1")]
    pub room_prefix: ::prost::alloc::string::String,
    /// Optional pin required to enter room
    #[prost(string, tag="2")]
    pub pin: ::prost::alloc::string::String,
    /// Optionally append random suffix
    #[prost(bool, tag="3")]
    pub randomize: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipDispatchRule {
    #[prost(oneof="sip_dispatch_rule::Rule", tags="1, 2, 3")]
    pub rule: ::core::option::Option<sip_dispatch_rule::Rule>,
}
/// Nested message and enum types in `SIPDispatchRule`.
pub mod sip_dispatch_rule {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Rule {
        /// SIPDispatchRuleDirect is a `SIP Dispatch Rule` that puts a user directly into a room
        /// This places users into an existing room. Optionally you can require a pin before a user can
        /// enter the room
        #[prost(message, tag="1")]
        DispatchRuleDirect(super::SipDispatchRuleDirect),
        /// SIPDispatchRuleIndividual is a `SIP Dispatch Rule` that creates a new room for each caller.
        #[prost(message, tag="2")]
        DispatchRuleIndividual(super::SipDispatchRuleIndividual),
        /// SIPDispatchRuleCallee is a `SIP Dispatch Rule` that creates a new room for each callee.
        #[prost(message, tag="3")]
        DispatchRuleCallee(super::SipDispatchRuleCallee),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CreateSipDispatchRuleRequest {
    /// Rule ID is ignored
    #[prost(message, optional, tag="10")]
    pub dispatch_rule: ::core::option::Option<SipDispatchRuleInfo>,
    #[deprecated]
    #[prost(message, optional, tag="1")]
    pub rule: ::core::option::Option<SipDispatchRule>,
    /// What trunks are accepted for this dispatch rule
    /// If empty all trunks will match this dispatch rule
    #[deprecated]
    #[prost(string, repeated, tag="2")]
    pub trunk_ids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// By default the From value (Phone number) is used for participant name/identity and added to attributes.
    /// If true, a random value for identity will be used and numbers will be omitted from attributes.
    #[deprecated]
    #[prost(bool, tag="3")]
    pub hide_phone_number: bool,
    /// Dispatch Rule will only accept a call made to these numbers (if set).
    #[deprecated]
    #[prost(string, repeated, tag="6")]
    pub inbound_numbers: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// Optional human-readable name for the Dispatch Rule.
    #[deprecated]
    #[prost(string, tag="4")]
    pub name: ::prost::alloc::string::String,
    /// User-defined metadata for the Dispatch Rule.
    /// Participants created by this rule will inherit this metadata.
    #[deprecated]
    #[prost(string, tag="5")]
    pub metadata: ::prost::alloc::string::String,
    /// User-defined attributes for the Dispatch Rule.
    /// Participants created by this rule will inherit these attributes.
    #[prost(map="string, string", tag="7")]
    pub attributes: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    /// Cloud-only, config preset to use
    #[deprecated]
    #[prost(string, tag="8")]
    pub room_preset: ::prost::alloc::string::String,
    /// RoomConfiguration to use if the participant initiates the room
    #[deprecated]
    #[prost(message, optional, tag="9")]
    pub room_config: ::core::option::Option<RoomConfiguration>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct UpdateSipDispatchRuleRequest {
    #[prost(string, tag="1")]
    pub sip_dispatch_rule_id: ::prost::alloc::string::String,
    #[prost(oneof="update_sip_dispatch_rule_request::Action", tags="2, 3")]
    pub action: ::core::option::Option<update_sip_dispatch_rule_request::Action>,
}
/// Nested message and enum types in `UpdateSIPDispatchRuleRequest`.
pub mod update_sip_dispatch_rule_request {
    #[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Action {
        #[prost(message, tag="2")]
        Replace(super::SipDispatchRuleInfo),
        #[prost(message, tag="3")]
        Update(super::SipDispatchRuleUpdate),
    }
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipDispatchRuleInfo {
    #[prost(string, tag="1")]
    pub sip_dispatch_rule_id: ::prost::alloc::string::String,
    #[prost(message, optional, tag="2")]
    pub rule: ::core::option::Option<SipDispatchRule>,
    #[prost(string, repeated, tag="3")]
    pub trunk_ids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(bool, tag="4")]
    pub hide_phone_number: bool,
    /// Dispatch Rule will only accept a call made to these numbers (if set).
    #[prost(string, repeated, tag="7")]
    pub inbound_numbers: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// Human-readable name for the Dispatch Rule.
    #[prost(string, tag="5")]
    pub name: ::prost::alloc::string::String,
    /// User-defined metadata for the Dispatch Rule.
    /// Participants created by this rule will inherit this metadata.
    #[prost(string, tag="6")]
    pub metadata: ::prost::alloc::string::String,
    /// User-defined attributes for the Dispatch Rule.
    /// Participants created by this rule will inherit these attributes.
    #[prost(map="string, string", tag="8")]
    pub attributes: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    /// Cloud-only, config preset to use
    #[prost(string, tag="9")]
    pub room_preset: ::prost::alloc::string::String,
    /// RoomConfiguration to use if the participant initiates the room
    #[prost(message, optional, tag="10")]
    pub room_config: ::core::option::Option<RoomConfiguration>,
    #[prost(bool, tag="11")]
    pub krisp_enabled: bool,
    /// NEXT ID: 13
    #[prost(enumeration="SipMediaEncryption", tag="12")]
    pub media_encryption: i32,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipDispatchRuleUpdate {
    #[prost(message, optional, tag="1")]
    pub trunk_ids: ::core::option::Option<ListUpdate>,
    #[prost(message, optional, tag="2")]
    pub rule: ::core::option::Option<SipDispatchRule>,
    #[prost(string, optional, tag="3")]
    pub name: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(string, optional, tag="4")]
    pub metadata: ::core::option::Option<::prost::alloc::string::String>,
    #[prost(map="string, string", tag="5")]
    pub attributes: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    #[prost(enumeration="SipMediaEncryption", optional, tag="6")]
    pub media_encryption: ::core::option::Option<i32>,
}
/// ListSIPDispatchRuleRequest lists dispatch rules for given filters. If no filters are set, all rules are listed.
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListSipDispatchRuleRequest {
    #[prost(message, optional, tag="3")]
    pub page: ::core::option::Option<Pagination>,
    /// Rule IDs to list. If this option is set, the response will contains rules in the same order.
    /// If any of the rules is missing, a nil item in that position will be sent in the response.
    #[prost(string, repeated, tag="1")]
    pub dispatch_rule_ids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    /// Only list rules that contain one of the Trunk IDs, including wildcard rules.
    #[prost(string, repeated, tag="2")]
    pub trunk_ids: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct ListSipDispatchRuleResponse {
    #[prost(message, repeated, tag="1")]
    pub items: ::prost::alloc::vec::Vec<SipDispatchRuleInfo>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct DeleteSipDispatchRuleRequest {
    #[prost(string, tag="1")]
    pub sip_dispatch_rule_id: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipOutboundConfig {
    /// SIP server address
    #[prost(string, tag="1")]
    pub hostname: ::prost::alloc::string::String,
    /// country where the call terminates as ISO 3166-1 alpha-2 (<https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2>). This will be used by the livekit infrastructure to route calls.
    #[prost(string, tag="7")]
    pub destination_country: ::prost::alloc::string::String,
    /// SIP Transport used for outbound call.
    #[prost(enumeration="SipTransport", tag="2")]
    pub transport: i32,
    /// Username and password used to authenticate with SIP server.
    /// May be empty to have no authentication.
    #[prost(string, tag="3")]
    pub auth_username: ::prost::alloc::string::String,
    #[prost(string, tag="4")]
    pub auth_password: ::prost::alloc::string::String,
    /// Map SIP X-* headers from 200 OK to SIP participant attributes.
    /// Keys are the names of X-* headers and values are the names of attributes they will be mapped to.
    #[prost(map="string, string", tag="5")]
    pub headers_to_attributes: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    /// Map LiveKit attributes to SIP X-* headers when sending BYE or REFER requests.
    /// Keys are the names of attributes and values are the names of X-* headers they will be mapped to.
    #[prost(map="string, string", tag="6")]
    pub attributes_to_headers: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
}
/// A SIP Participant is a singular SIP session connected to a LiveKit room via
/// a SIP Trunk into a SIP DispatchRule
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct CreateSipParticipantRequest {
    /// What SIP Trunk should be used to dial the user
    #[prost(string, tag="1")]
    pub sip_trunk_id: ::prost::alloc::string::String,
    #[prost(message, optional, tag="20")]
    pub trunk: ::core::option::Option<SipOutboundConfig>,
    /// What number should be dialed via SIP
    #[prost(string, tag="2")]
    pub sip_call_to: ::prost::alloc::string::String,
    /// Optional SIP From number to use. If empty, trunk number is used.
    #[prost(string, tag="15")]
    pub sip_number: ::prost::alloc::string::String,
    /// What LiveKit room should this participant be connected too
    #[prost(string, tag="3")]
    pub room_name: ::prost::alloc::string::String,
    /// Optional identity of the participant in LiveKit room
    #[prost(string, tag="4")]
    pub participant_identity: ::prost::alloc::string::String,
    /// Optional name of the participant in LiveKit room
    #[prost(string, tag="7")]
    pub participant_name: ::prost::alloc::string::String,
    /// Optional user-defined metadata. Will be attached to a created Participant in the room.
    #[prost(string, tag="8")]
    pub participant_metadata: ::prost::alloc::string::String,
    /// Optional user-defined attributes. Will be attached to a created Participant in the room.
    #[prost(map="string, string", tag="9")]
    pub participant_attributes: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    /// Optionally send following DTMF digits (extension codes) when making a call.
    /// Character 'w' can be used to add a 0.5 sec delay.
    #[prost(string, tag="5")]
    pub dtmf: ::prost::alloc::string::String,
    /// Optionally play dialtone in the room as an audible indicator for existing participants. The `play_ringtone` option is deprectated but has the same effect.
    #[deprecated]
    #[prost(bool, tag="6")]
    pub play_ringtone: bool,
    #[prost(bool, tag="13")]
    pub play_dialtone: bool,
    /// By default the From value (Phone number) is used for participant name/identity (if not set) and added to attributes.
    /// If true, a random value for identity will be used and numbers will be omitted from attributes.
    #[prost(bool, tag="10")]
    pub hide_phone_number: bool,
    /// These headers are sent as-is and may help identify this call as coming from LiveKit for the other SIP endpoint.
    #[prost(map="string, string", tag="16")]
    pub headers: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    /// Map SIP headers from 200 OK to sip.h.* participant attributes automatically.
    ///
    /// When the names of required headers is known, using headers_to_attributes is strongly recommended.
    ///
    /// When mapping 200 OK headers to follow-up request headers with attributes_to_headers map,
    /// lowercase header names should be used, for example: sip.h.x-custom-header.
    #[prost(enumeration="SipHeaderOptions", tag="17")]
    pub include_headers: i32,
    /// Max time for the callee to answer the call.
    #[prost(message, optional, tag="11")]
    pub ringing_timeout: ::core::option::Option<::pbjson_types::Duration>,
    /// Max call duration.
    #[prost(message, optional, tag="12")]
    pub max_call_duration: ::core::option::Option<::pbjson_types::Duration>,
    /// Enable voice isolation for the callee.
    #[prost(bool, tag="14")]
    pub krisp_enabled: bool,
    #[prost(enumeration="SipMediaEncryption", tag="18")]
    pub media_encryption: i32,
    /// Wait for the answer for the call before returning.
    ///
    /// NEXT ID: 21
    #[prost(bool, tag="19")]
    pub wait_until_answered: bool,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipParticipantInfo {
    #[prost(string, tag="1")]
    pub participant_id: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub participant_identity: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub room_name: ::prost::alloc::string::String,
    #[prost(string, tag="4")]
    pub sip_call_id: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct TransferSipParticipantRequest {
    #[prost(string, tag="1")]
    pub participant_identity: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub room_name: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub transfer_to: ::prost::alloc::string::String,
    /// Optionally play dialtone to the SIP participant as an audible indicator of being transferred
    #[prost(bool, tag="4")]
    pub play_dialtone: bool,
    /// Add the following headers to the REFER SIP request.
    #[prost(map="string, string", tag="5")]
    pub headers: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    /// Max time for the transfer destination to answer the call.
    #[prost(message, optional, tag="6")]
    pub ringing_timeout: ::core::option::Option<::pbjson_types::Duration>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipCallInfo {
    #[prost(string, tag="1")]
    pub call_id: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub trunk_id: ::prost::alloc::string::String,
    #[prost(string, tag="16")]
    pub dispatch_rule_id: ::prost::alloc::string::String,
    #[prost(string, tag="17")]
    pub region: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub room_name: ::prost::alloc::string::String,
    /// ID of the current/previous room published to
    #[prost(string, tag="4")]
    pub room_id: ::prost::alloc::string::String,
    #[prost(string, tag="5")]
    pub participant_identity: ::prost::alloc::string::String,
    #[prost(map="string, string", tag="18")]
    pub participant_attributes: ::std::collections::HashMap<::prost::alloc::string::String, ::prost::alloc::string::String>,
    #[prost(message, optional, tag="6")]
    pub from_uri: ::core::option::Option<SipUri>,
    #[prost(message, optional, tag="7")]
    pub to_uri: ::core::option::Option<SipUri>,
    #[deprecated]
    #[prost(int64, tag="9")]
    pub created_at: i64,
    #[deprecated]
    #[prost(int64, tag="10")]
    pub started_at: i64,
    #[deprecated]
    #[prost(int64, tag="11")]
    pub ended_at: i64,
    #[prost(enumeration="SipFeature", repeated, tag="14")]
    pub enabled_features: ::prost::alloc::vec::Vec<i32>,
    #[prost(enumeration="SipCallDirection", tag="15")]
    pub call_direction: i32,
    #[prost(enumeration="SipCallStatus", tag="8")]
    pub call_status: i32,
    #[prost(int64, tag="22")]
    pub created_at_ns: i64,
    #[prost(int64, tag="23")]
    pub started_at_ns: i64,
    #[prost(int64, tag="24")]
    pub ended_at_ns: i64,
    #[prost(enumeration="DisconnectReason", tag="12")]
    pub disconnect_reason: i32,
    #[prost(string, tag="13")]
    pub error: ::prost::alloc::string::String,
    #[prost(message, optional, tag="19")]
    pub call_status_code: ::core::option::Option<SipStatus>,
    #[prost(string, tag="20")]
    pub audio_codec: ::prost::alloc::string::String,
    #[prost(string, tag="21")]
    pub media_encryption: ::prost::alloc::string::String,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipTransferInfo {
    #[prost(string, tag="1")]
    pub transfer_id: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub call_id: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub transfer_to: ::prost::alloc::string::String,
    #[prost(int64, tag="4")]
    pub transfer_initiated_at_ns: i64,
    #[prost(int64, tag="5")]
    pub transfer_completed_at_ns: i64,
    #[prost(enumeration="SipTransferStatus", tag="6")]
    pub transfer_status: i32,
    #[prost(string, tag="7")]
    pub error: ::prost::alloc::string::String,
    #[prost(message, optional, tag="8")]
    pub transfer_status_code: ::core::option::Option<SipStatus>,
}
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SipUri {
    #[prost(string, tag="1")]
    pub user: ::prost::alloc::string::String,
    #[prost(string, tag="2")]
    pub host: ::prost::alloc::string::String,
    #[prost(string, tag="3")]
    pub ip: ::prost::alloc::string::String,
    #[prost(uint32, tag="4")]
    pub port: u32,
    #[prost(enumeration="SipTransport", tag="5")]
    pub transport: i32,
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum SipStatusCode {
    SipStatusUnknown = 0,
    SipStatusTrying = 100,
    SipStatusRinging = 180,
    SipStatusCallIsForwarded = 181,
    SipStatusQueued = 182,
    SipStatusSessionProgress = 183,
    SipStatusOk = 200,
    SipStatusAccepted = 202,
    SipStatusMovedPermanently = 301,
    SipStatusMovedTemporarily = 302,
    SipStatusUseProxy = 305,
    SipStatusBadRequest = 400,
    SipStatusUnauthorized = 401,
    SipStatusPaymentRequired = 402,
    SipStatusForbidden = 403,
    SipStatusNotfound = 404,
    SipStatusMethodNotAllowed = 405,
    SipStatusNotAcceptable = 406,
    SipStatusProxyAuthRequired = 407,
    SipStatusRequestTimeout = 408,
    SipStatusConflict = 409,
    SipStatusGone = 410,
    SipStatusRequestEntityTooLarge = 413,
    SipStatusRequestUriTooLong = 414,
    SipStatusUnsupportedMediaType = 415,
    SipStatusRequestedRangeNotSatisfiable = 416,
    SipStatusBadExtension = 420,
    SipStatusExtensionRequired = 421,
    SipStatusIntervalTooBrief = 423,
    SipStatusTemporarilyUnavailable = 480,
    SipStatusCallTransactionDoesNotExists = 481,
    SipStatusLoopDetected = 482,
    SipStatusTooManyHops = 483,
    SipStatusAddressIncomplete = 484,
    SipStatusAmbiguous = 485,
    SipStatusBusyHere = 486,
    SipStatusRequestTerminated = 487,
    SipStatusNotAcceptableHere = 488,
    SipStatusInternalServerError = 500,
    SipStatusNotImplemented = 501,
    SipStatusBadGateway = 502,
    SipStatusServiceUnavailable = 503,
    SipStatusGatewayTimeout = 504,
    SipStatusVersionNotSupported = 505,
    SipStatusMessageTooLarge = 513,
    SipStatusGlobalBusyEverywhere = 600,
    SipStatusGlobalDecline = 603,
    SipStatusGlobalDoesNotExistAnywhere = 604,
    SipStatusGlobalNotAcceptable = 606,
}
impl SipStatusCode {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            SipStatusCode::SipStatusUnknown => "SIP_STATUS_UNKNOWN",
            SipStatusCode::SipStatusTrying => "SIP_STATUS_TRYING",
            SipStatusCode::SipStatusRinging => "SIP_STATUS_RINGING",
            SipStatusCode::SipStatusCallIsForwarded => "SIP_STATUS_CALL_IS_FORWARDED",
            SipStatusCode::SipStatusQueued => "SIP_STATUS_QUEUED",
            SipStatusCode::SipStatusSessionProgress => "SIP_STATUS_SESSION_PROGRESS",
            SipStatusCode::SipStatusOk => "SIP_STATUS_OK",
            SipStatusCode::SipStatusAccepted => "SIP_STATUS_ACCEPTED",
            SipStatusCode::SipStatusMovedPermanently => "SIP_STATUS_MOVED_PERMANENTLY",
            SipStatusCode::SipStatusMovedTemporarily => "SIP_STATUS_MOVED_TEMPORARILY",
            SipStatusCode::SipStatusUseProxy => "SIP_STATUS_USE_PROXY",
            SipStatusCode::SipStatusBadRequest => "SIP_STATUS_BAD_REQUEST",
            SipStatusCode::SipStatusUnauthorized => "SIP_STATUS_UNAUTHORIZED",
            SipStatusCode::SipStatusPaymentRequired => "SIP_STATUS_PAYMENT_REQUIRED",
            SipStatusCode::SipStatusForbidden => "SIP_STATUS_FORBIDDEN",
            SipStatusCode::SipStatusNotfound => "SIP_STATUS_NOTFOUND",
            SipStatusCode::SipStatusMethodNotAllowed => "SIP_STATUS_METHOD_NOT_ALLOWED",
            SipStatusCode::SipStatusNotAcceptable => "SIP_STATUS_NOT_ACCEPTABLE",
            SipStatusCode::SipStatusProxyAuthRequired => "SIP_STATUS_PROXY_AUTH_REQUIRED",
            SipStatusCode::SipStatusRequestTimeout => "SIP_STATUS_REQUEST_TIMEOUT",
            SipStatusCode::SipStatusConflict => "SIP_STATUS_CONFLICT",
            SipStatusCode::SipStatusGone => "SIP_STATUS_GONE",
            SipStatusCode::SipStatusRequestEntityTooLarge => "SIP_STATUS_REQUEST_ENTITY_TOO_LARGE",
            SipStatusCode::SipStatusRequestUriTooLong => "SIP_STATUS_REQUEST_URI_TOO_LONG",
            SipStatusCode::SipStatusUnsupportedMediaType => "SIP_STATUS_UNSUPPORTED_MEDIA_TYPE",
            SipStatusCode::SipStatusRequestedRangeNotSatisfiable => "SIP_STATUS_REQUESTED_RANGE_NOT_SATISFIABLE",
            SipStatusCode::SipStatusBadExtension => "SIP_STATUS_BAD_EXTENSION",
            SipStatusCode::SipStatusExtensionRequired => "SIP_STATUS_EXTENSION_REQUIRED",
            SipStatusCode::SipStatusIntervalTooBrief => "SIP_STATUS_INTERVAL_TOO_BRIEF",
            SipStatusCode::SipStatusTemporarilyUnavailable => "SIP_STATUS_TEMPORARILY_UNAVAILABLE",
            SipStatusCode::SipStatusCallTransactionDoesNotExists => "SIP_STATUS_CALL_TRANSACTION_DOES_NOT_EXISTS",
            SipStatusCode::SipStatusLoopDetected => "SIP_STATUS_LOOP_DETECTED",
            SipStatusCode::SipStatusTooManyHops => "SIP_STATUS_TOO_MANY_HOPS",
            SipStatusCode::SipStatusAddressIncomplete => "SIP_STATUS_ADDRESS_INCOMPLETE",
            SipStatusCode::SipStatusAmbiguous => "SIP_STATUS_AMBIGUOUS",
            SipStatusCode::SipStatusBusyHere => "SIP_STATUS_BUSY_HERE",
            SipStatusCode::SipStatusRequestTerminated => "SIP_STATUS_REQUEST_TERMINATED",
            SipStatusCode::SipStatusNotAcceptableHere => "SIP_STATUS_NOT_ACCEPTABLE_HERE",
            SipStatusCode::SipStatusInternalServerError => "SIP_STATUS_INTERNAL_SERVER_ERROR",
            SipStatusCode::SipStatusNotImplemented => "SIP_STATUS_NOT_IMPLEMENTED",
            SipStatusCode::SipStatusBadGateway => "SIP_STATUS_BAD_GATEWAY",
            SipStatusCode::SipStatusServiceUnavailable => "SIP_STATUS_SERVICE_UNAVAILABLE",
            SipStatusCode::SipStatusGatewayTimeout => "SIP_STATUS_GATEWAY_TIMEOUT",
            SipStatusCode::SipStatusVersionNotSupported => "SIP_STATUS_VERSION_NOT_SUPPORTED",
            SipStatusCode::SipStatusMessageTooLarge => "SIP_STATUS_MESSAGE_TOO_LARGE",
            SipStatusCode::SipStatusGlobalBusyEverywhere => "SIP_STATUS_GLOBAL_BUSY_EVERYWHERE",
            SipStatusCode::SipStatusGlobalDecline => "SIP_STATUS_GLOBAL_DECLINE",
            SipStatusCode::SipStatusGlobalDoesNotExistAnywhere => "SIP_STATUS_GLOBAL_DOES_NOT_EXIST_ANYWHERE",
            SipStatusCode::SipStatusGlobalNotAcceptable => "SIP_STATUS_GLOBAL_NOT_ACCEPTABLE",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "SIP_STATUS_UNKNOWN" => Some(Self::SipStatusUnknown),
            "SIP_STATUS_TRYING" => Some(Self::SipStatusTrying),
            "SIP_STATUS_RINGING" => Some(Self::SipStatusRinging),
            "SIP_STATUS_CALL_IS_FORWARDED" => Some(Self::SipStatusCallIsForwarded),
            "SIP_STATUS_QUEUED" => Some(Self::SipStatusQueued),
            "SIP_STATUS_SESSION_PROGRESS" => Some(Self::SipStatusSessionProgress),
            "SIP_STATUS_OK" => Some(Self::SipStatusOk),
            "SIP_STATUS_ACCEPTED" => Some(Self::SipStatusAccepted),
            "SIP_STATUS_MOVED_PERMANENTLY" => Some(Self::SipStatusMovedPermanently),
            "SIP_STATUS_MOVED_TEMPORARILY" => Some(Self::SipStatusMovedTemporarily),
            "SIP_STATUS_USE_PROXY" => Some(Self::SipStatusUseProxy),
            "SIP_STATUS_BAD_REQUEST" => Some(Self::SipStatusBadRequest),
            "SIP_STATUS_UNAUTHORIZED" => Some(Self::SipStatusUnauthorized),
            "SIP_STATUS_PAYMENT_REQUIRED" => Some(Self::SipStatusPaymentRequired),
            "SIP_STATUS_FORBIDDEN" => Some(Self::SipStatusForbidden),
            "SIP_STATUS_NOTFOUND" => Some(Self::SipStatusNotfound),
            "SIP_STATUS_METHOD_NOT_ALLOWED" => Some(Self::SipStatusMethodNotAllowed),
            "SIP_STATUS_NOT_ACCEPTABLE" => Some(Self::SipStatusNotAcceptable),
            "SIP_STATUS_PROXY_AUTH_REQUIRED" => Some(Self::SipStatusProxyAuthRequired),
            "SIP_STATUS_REQUEST_TIMEOUT" => Some(Self::SipStatusRequestTimeout),
            "SIP_STATUS_CONFLICT" => Some(Self::SipStatusConflict),
            "SIP_STATUS_GONE" => Some(Self::SipStatusGone),
            "SIP_STATUS_REQUEST_ENTITY_TOO_LARGE" => Some(Self::SipStatusRequestEntityTooLarge),
            "SIP_STATUS_REQUEST_URI_TOO_LONG" => Some(Self::SipStatusRequestUriTooLong),
            "SIP_STATUS_UNSUPPORTED_MEDIA_TYPE" => Some(Self::SipStatusUnsupportedMediaType),
            "SIP_STATUS_REQUESTED_RANGE_NOT_SATISFIABLE" => Some(Self::SipStatusRequestedRangeNotSatisfiable),
            "SIP_STATUS_BAD_EXTENSION" => Some(Self::SipStatusBadExtension),
            "SIP_STATUS_EXTENSION_REQUIRED" => Some(Self::SipStatusExtensionRequired),
            "SIP_STATUS_INTERVAL_TOO_BRIEF" => Some(Self::SipStatusIntervalTooBrief),
            "SIP_STATUS_TEMPORARILY_UNAVAILABLE" => Some(Self::SipStatusTemporarilyUnavailable),
            "SIP_STATUS_CALL_TRANSACTION_DOES_NOT_EXISTS" => Some(Self::SipStatusCallTransactionDoesNotExists),
            "SIP_STATUS_LOOP_DETECTED" => Some(Self::SipStatusLoopDetected),
            "SIP_STATUS_TOO_MANY_HOPS" => Some(Self::SipStatusTooManyHops),
            "SIP_STATUS_ADDRESS_INCOMPLETE" => Some(Self::SipStatusAddressIncomplete),
            "SIP_STATUS_AMBIGUOUS" => Some(Self::SipStatusAmbiguous),
            "SIP_STATUS_BUSY_HERE" => Some(Self::SipStatusBusyHere),
            "SIP_STATUS_REQUEST_TERMINATED" => Some(Self::SipStatusRequestTerminated),
            "SIP_STATUS_NOT_ACCEPTABLE_HERE" => Some(Self::SipStatusNotAcceptableHere),
            "SIP_STATUS_INTERNAL_SERVER_ERROR" => Some(Self::SipStatusInternalServerError),
            "SIP_STATUS_NOT_IMPLEMENTED" => Some(Self::SipStatusNotImplemented),
            "SIP_STATUS_BAD_GATEWAY" => Some(Self::SipStatusBadGateway),
            "SIP_STATUS_SERVICE_UNAVAILABLE" => Some(Self::SipStatusServiceUnavailable),
            "SIP_STATUS_GATEWAY_TIMEOUT" => Some(Self::SipStatusGatewayTimeout),
            "SIP_STATUS_VERSION_NOT_SUPPORTED" => Some(Self::SipStatusVersionNotSupported),
            "SIP_STATUS_MESSAGE_TOO_LARGE" => Some(Self::SipStatusMessageTooLarge),
            "SIP_STATUS_GLOBAL_BUSY_EVERYWHERE" => Some(Self::SipStatusGlobalBusyEverywhere),
            "SIP_STATUS_GLOBAL_DECLINE" => Some(Self::SipStatusGlobalDecline),
            "SIP_STATUS_GLOBAL_DOES_NOT_EXIST_ANYWHERE" => Some(Self::SipStatusGlobalDoesNotExistAnywhere),
            "SIP_STATUS_GLOBAL_NOT_ACCEPTABLE" => Some(Self::SipStatusGlobalNotAcceptable),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum SipTransport {
    Auto = 0,
    Udp = 1,
    Tcp = 2,
    Tls = 3,
}
impl SipTransport {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            SipTransport::Auto => "SIP_TRANSPORT_AUTO",
            SipTransport::Udp => "SIP_TRANSPORT_UDP",
            SipTransport::Tcp => "SIP_TRANSPORT_TCP",
            SipTransport::Tls => "SIP_TRANSPORT_TLS",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "SIP_TRANSPORT_AUTO" => Some(Self::Auto),
            "SIP_TRANSPORT_UDP" => Some(Self::Udp),
            "SIP_TRANSPORT_TCP" => Some(Self::Tcp),
            "SIP_TRANSPORT_TLS" => Some(Self::Tls),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum SipHeaderOptions {
    /// do not map any headers, except ones mapped explicitly
    SipNoHeaders = 0,
    /// map all X-* headers to sip.h.x-* attributes
    SipXHeaders = 1,
    /// map all headers to sip.h.* attributes
    SipAllHeaders = 2,
}
impl SipHeaderOptions {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            SipHeaderOptions::SipNoHeaders => "SIP_NO_HEADERS",
            SipHeaderOptions::SipXHeaders => "SIP_X_HEADERS",
            SipHeaderOptions::SipAllHeaders => "SIP_ALL_HEADERS",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "SIP_NO_HEADERS" => Some(Self::SipNoHeaders),
            "SIP_X_HEADERS" => Some(Self::SipXHeaders),
            "SIP_ALL_HEADERS" => Some(Self::SipAllHeaders),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum SipMediaEncryption {
    /// do not enable encryption
    SipMediaEncryptDisable = 0,
    /// use encryption if available
    SipMediaEncryptAllow = 1,
    /// require encryption
    SipMediaEncryptRequire = 2,
}
impl SipMediaEncryption {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            SipMediaEncryption::SipMediaEncryptDisable => "SIP_MEDIA_ENCRYPT_DISABLE",
            SipMediaEncryption::SipMediaEncryptAllow => "SIP_MEDIA_ENCRYPT_ALLOW",
            SipMediaEncryption::SipMediaEncryptRequire => "SIP_MEDIA_ENCRYPT_REQUIRE",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "SIP_MEDIA_ENCRYPT_DISABLE" => Some(Self::SipMediaEncryptDisable),
            "SIP_MEDIA_ENCRYPT_ALLOW" => Some(Self::SipMediaEncryptAllow),
            "SIP_MEDIA_ENCRYPT_REQUIRE" => Some(Self::SipMediaEncryptRequire),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum SipCallStatus {
    /// Incoming call is being handled by the SIP service. The SIP participant hasn't joined a LiveKit room yet
    ScsCallIncoming = 0,
    /// SIP participant for outgoing call has been created. The SIP outgoing call is being established
    ScsParticipantJoined = 1,
    /// Call is ongoing. SIP participant is active in the LiveKit room
    ScsActive = 2,
    /// Call has ended
    ScsDisconnected = 3,
    /// Call has ended or never succeeded because of an error
    ScsError = 4,
}
impl SipCallStatus {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            SipCallStatus::ScsCallIncoming => "SCS_CALL_INCOMING",
            SipCallStatus::ScsParticipantJoined => "SCS_PARTICIPANT_JOINED",
            SipCallStatus::ScsActive => "SCS_ACTIVE",
            SipCallStatus::ScsDisconnected => "SCS_DISCONNECTED",
            SipCallStatus::ScsError => "SCS_ERROR",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "SCS_CALL_INCOMING" => Some(Self::ScsCallIncoming),
            "SCS_PARTICIPANT_JOINED" => Some(Self::ScsParticipantJoined),
            "SCS_ACTIVE" => Some(Self::ScsActive),
            "SCS_DISCONNECTED" => Some(Self::ScsDisconnected),
            "SCS_ERROR" => Some(Self::ScsError),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum SipTransferStatus {
    StsTransferOngoing = 0,
    StsTransferFailed = 1,
    StsTransferSuccessful = 2,
}
impl SipTransferStatus {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            SipTransferStatus::StsTransferOngoing => "STS_TRANSFER_ONGOING",
            SipTransferStatus::StsTransferFailed => "STS_TRANSFER_FAILED",
            SipTransferStatus::StsTransferSuccessful => "STS_TRANSFER_SUCCESSFUL",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "STS_TRANSFER_ONGOING" => Some(Self::StsTransferOngoing),
            "STS_TRANSFER_FAILED" => Some(Self::StsTransferFailed),
            "STS_TRANSFER_SUCCESSFUL" => Some(Self::StsTransferSuccessful),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum SipFeature {
    None = 0,
    KrispEnabled = 1,
}
impl SipFeature {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            SipFeature::None => "NONE",
            SipFeature::KrispEnabled => "KRISP_ENABLED",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "NONE" => Some(Self::None),
            "KRISP_ENABLED" => Some(Self::KrispEnabled),
            _ => None,
        }
    }
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum SipCallDirection {
    ScdUnknown = 0,
    ScdInbound = 1,
    ScdOutbound = 2,
}
impl SipCallDirection {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            SipCallDirection::ScdUnknown => "SCD_UNKNOWN",
            SipCallDirection::ScdInbound => "SCD_INBOUND",
            SipCallDirection::ScdOutbound => "SCD_OUTBOUND",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "SCD_UNKNOWN" => Some(Self::ScdUnknown),
            "SCD_INBOUND" => Some(Self::ScdInbound),
            "SCD_OUTBOUND" => Some(Self::ScdOutbound),
            _ => None,
        }
    }
}
include!("livekit.serde.rs");
// @@protoc_insertion_point(module)
