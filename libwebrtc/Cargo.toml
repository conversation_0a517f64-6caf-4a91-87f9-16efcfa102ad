[package]
name = "libwebrtc"
version = "0.3.12"
edition = "2021"
homepage = "https://livekit.io"
license = "Apache-2.0"
description = "Livekit safe bindings to libwebrtc"
repository = "https://github.com/livekit/rust-sdks"

[dependencies]
livekit-protocol = { workspace = true }
log = "0.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
thiserror = "1.0"

[target.'cfg(target_os = "android")'.dependencies]
jni = "0.21"

[target.'cfg(not(target_arch = "wasm32"))'.dependencies]
webrtc-sys = { workspace = true }
livekit-runtime = { workspace = true }
lazy_static = "1.4"
parking_lot = { version = "0.12" }
tokio = { version = "1", default-features = false, features = ["sync", "macros"] }
cxx = "1.0"

[target.'cfg(target_arch = "wasm32")'.dependencies]
wasm-bindgen = "0.2"
js-sys = "0.3"
wasm-bindgen-futures = "0.4"
web-sys = { version = "0.3", features = [
  "MessageEvent",
  "RtcPeerConnection",
  "RtcSignalingState",
  "RtcSdpType",
  "RtcSessionDescriptionInit",
  "RtcPeerConnectionIceEvent",
  "RtcIceCandidate",
  "RtcDataChannel",
  "RtcDataChannelEvent",
  "RtcDataChannelState",
  "EventTarget",
  "WebGlRenderingContext",
  "WebGlTexture",
] }

[dev-dependencies]
env_logger = "0.10"
