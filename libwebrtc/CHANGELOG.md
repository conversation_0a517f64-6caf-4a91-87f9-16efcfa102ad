# Changelog

## [0.3.12](https://github.com/livekit/rust-sdks/compare/rust-sdks/<EMAIL>-sdks/libwebrtc@0.3.12) - 2025-06-17

### Other

- updated the following local packages: livekit-protocol, webrtc-sys

## [0.3.11](https://github.com/livekit/rust-sdks/compare/rust-sdks/<EMAIL>-sdks/libwebrtc@0.3.11) - 2025-06-11

### Fixed

- fix uint32 overflow ([#615](https://github.com/livekit/rust-sdks/pull/615))

### Other

- remove ([#633](https://github.com/livekit/rust-sdks/pull/633))
- expose apm stream_delay ([#616](https://github.com/livekit/rust-sdks/pull/616))
- Add i420_to_nv12 ([#605](https://github.com/livekit/rust-sdks/pull/605))
- ffi-v0.13.0 ([#590](https://github.com/livekit/rust-sdks/pull/590))
- add AudioProcessingModule ([#580](https://github.com/livekit/rust-sdks/pull/580))

## [0.3.10] - 2025-02-05

### Fixed

- Fix build issue

## [0.3.9] - 2025-01-17

### Added

- Expose DataChannel.bufferedAmount property

## [0.3.8] - 2024-12-14

### Added

- bump libwebrtc to m125
