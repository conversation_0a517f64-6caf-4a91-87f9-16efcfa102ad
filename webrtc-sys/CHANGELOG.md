# Changelog

## [0.3.9](https://github.com/livekit/rust-sdks/compare/rust-sdks/<EMAIL>-sdks/webrtc-sys@0.3.9) - 2025-06-17

### Other

- updated the following local packages: webrtc-sys-build

## [0.3.8](https://github.com/livekit/rust-sdks/compare/rust-sdks/<EMAIL>-sdks/webrtc-sys@0.3.8) - 2025-06-11

### Fixed

- fix libwebrtc.jar build issue ([#586](https://github.com/livekit/rust-sdks/pull/586))

### Other

- bump version for webrtc (fix win CI) ([#650](https://github.com/livekit/rust-sdks/pull/650))
- try to fix webrtc build for iOS/macOS. ([#646](https://github.com/livekit/rust-sdks/pull/646))
- remove ([#633](https://github.com/livekit/rust-sdks/pull/633))
- expose apm stream_delay ([#616](https://github.com/livekit/rust-sdks/pull/616))
- Add i420_to_nv12 ([#605](https://github.com/livekit/rust-sdks/pull/605))
- ffi-v0.13.0 ([#590](https://github.com/livekit/rust-sdks/pull/590))
- add AudioProcessingModule ([#580](https://github.com/livekit/rust-sdks/pull/580))

## [0.3.7] - 2025-02-05

### Added

- Expose DataChannel.bufferedAmount property

## [0.3.6] - 2024-12-14

### Added

- bump libwebrtc to m125
