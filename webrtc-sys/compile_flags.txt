-xc++
-xobjective-c
-xobjective-c++
-std=c++17
-Iinclude
-Ilibwebrtc/src
-Ilibwebrtc/src/third_party/abseil-cpp
-Ilibwebrtc/src/third_party/libc++
-Ilibwebrtc/src/third_party/libyuv/include
-Ilibwebrtc/src/sdk/objc/base
-Ilibwebrtc/src/sdk/objc
-I../target/cxxbridge
-DWEBRTC_POSIX
-DWEBRTC_ANDROID
-DNDEBUG
-DWEBRTC_ENABLE_SYMBOL_EXPORT
-DWEBRTC_APM_DEBUG_DUMP=0
-D__ANDROID_API__=29
-DWEBRTC_LIBRARY_IMPL
--sysroot=/Users/<USER>/Library/Android/sdk/ndk-bundle/toolchains/llvm/prebuilt/darwin-x86_64/sysroot
--target=aarch64-none-linux-android
--gcc-toolchain=/Users/<USER>/Library/Android/sdk/ndk-bundle/toolchains/llvm/prebuilt/darwin-x86_64
