diff --git a/abseil-cpp/absl/base/options.h b/abseil-cpp/absl/base/options.h
index bd43b6ef0..ab5917e75 100644
--- a/abseil-cpp/absl/base/options.h
+++ b/abseil-cpp/absl/base/options.h
@@ -121,7 +121,7 @@
 // absl::optional is a typedef of std::optional, use the feature macro
 // ABSL_USES_STD_OPTIONAL.
 
-#define ABSL_OPTION_USE_STD_OPTIONAL 2
+#define ABSL_OPTION_USE_STD_OPTIONAL 0
 
 
 // ABSL_OPTION_USE_STD_STRING_VIEW
