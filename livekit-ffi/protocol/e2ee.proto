// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package livekit.proto;
option csharp_namespace = "LiveKit.Proto";

// TODO(theomonnom): Should FrameCryptor be stateful on the client side and have their own handle?

enum EncryptionType {
    NONE = 0;
    GCM = 1;
    CUSTOM = 2;
}

message FrameCryptor {
    required string participant_identity = 1;
    required string track_sid = 2;
    required int32 key_index = 3;
    required bool enabled = 4;
}

message KeyProviderOptions {
    // Only specify if you want to use a shared_key
    optional bytes shared_key = 1; 
    required int32 ratchet_window_size = 2;
    required bytes ratchet_salt = 3;
    required int32 failure_tolerance = 4; // -1 = no tolerance
}

message E2eeOptions {
    required EncryptionType encryption_type = 1;
    required KeyProviderOptions key_provider_options = 2;
}

enum EncryptionState {
    NEW = 0;
    OK = 1;
    ENCRYPTION_FAILED = 2;
    DECRYPTION_FAILED = 3;
    MISSING_KEY = 4;
    KEY_RATCHETED = 5;
    INTERNAL_ERROR = 6;
}

message E2eeManagerSetEnabledRequest {
    required bool enabled = 1;
}
message E2eeManagerSetEnabledResponse {}

message E2eeManagerGetFrameCryptorsRequest {}
message E2eeManagerGetFrameCryptorsResponse {
    repeated FrameCryptor frame_cryptors = 1;
}

message FrameCryptorSetEnabledRequest {
    required string participant_identity = 1;
    required string track_sid = 2;
    required bool enabled = 3;
}
message FrameCryptorSetEnabledResponse {}

message FrameCryptorSetKeyIndexRequest {
    required string participant_identity = 1;
    required string track_sid = 2;
    required int32 key_index = 3;
}
message FrameCryptorSetKeyIndexResponse {}

message SetSharedKeyRequest {
    required bytes shared_key = 1;
    required int32 key_index = 2;
}
message SetSharedKeyResponse {}

message RatchetSharedKeyRequest {
    required int32 key_index = 1;   
}
message RatchetSharedKeyResponse {
    optional bytes new_key = 1;
}

message GetSharedKeyRequest {
    required int32 key_index = 1;
}
message GetSharedKeyResponse {
    optional bytes key = 1;
}

message SetKeyRequest {
    required string participant_identity = 1;
    required bytes key = 2;
    required int32 key_index = 3;
}
message SetKeyResponse {}

message RatchetKeyRequest {
    required string participant_identity = 1;
    required int32 key_index = 2;
}
message RatchetKeyResponse {
    optional bytes new_key = 1;
}

message GetKeyRequest {
    required string participant_identity = 1;
    required int32 key_index = 2;
}
message GetKeyResponse {
    optional bytes key = 1;
}

message E2eeRequest {
    required uint64 room_handle = 1;
    oneof message {
        E2eeManagerSetEnabledRequest manager_set_enabled = 2;
        E2eeManagerGetFrameCryptorsRequest manager_get_frame_cryptors = 3;
        FrameCryptorSetEnabledRequest cryptor_set_enabled = 4;
        FrameCryptorSetKeyIndexRequest cryptor_set_key_index = 5;
        SetSharedKeyRequest set_shared_key = 6;
        RatchetSharedKeyRequest ratchet_shared_key = 7;
        GetSharedKeyRequest get_shared_key = 8;
        SetKeyRequest set_key = 9;
        RatchetKeyRequest ratchet_key = 10;
        GetKeyRequest get_key = 11;
    }
}

message E2eeResponse {
    oneof message {
        E2eeManagerSetEnabledResponse manager_set_enabled = 1;
        E2eeManagerGetFrameCryptorsResponse manager_get_frame_cryptors = 2;
        FrameCryptorSetEnabledResponse cryptor_set_enabled = 3;
        FrameCryptorSetKeyIndexResponse cryptor_set_key_index = 4;
        SetSharedKeyResponse set_shared_key = 5;
        RatchetSharedKeyResponse ratchet_shared_key = 6;
        GetSharedKeyResponse get_shared_key = 7;
        SetKeyResponse set_key = 8;
        RatchetKeyResponse ratchet_key = 9;
        GetKeyResponse get_key = 10;
    }
}
