// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package livekit.proto;
option csharp_namespace = "LiveKit.Proto";

import "handle.proto";

message ParticipantInfo {
  required string sid = 1;
  required string name = 2;
  required string identity = 3;
  required string metadata = 4;
  map<string, string> attributes = 5;
  required ParticipantKind kind = 6;
  required DisconnectReason disconnect_reason = 7;
}

message OwnedParticipant {
  required FfiOwnedHandle handle = 1;
  required ParticipantInfo info = 2;
}

enum ParticipantKind {
  PARTICIPANT_KIND_STANDARD = 0;
  PARTICIPANT_KIND_INGRESS = 1;
  PARTICIPANT_KIND_EGRESS = 2;
  PARTICIPANT_KIND_SIP = 3;
  PARTICIPANT_KIND_AGENT = 4;
}

enum DisconnectReason {
  UNKNOWN_REASON = 0;
  // the client initiated the disconnect
  CLIENT_INITIATED = 1;
  // another participant with the same identity has joined the room
  DUPLICATE_IDENTITY = 2;
  // the server instance is shutting down
  SERVER_SHUTDOWN = 3;
  // RoomService.RemoveParticipant was called
  PARTICIPANT_REMOVED = 4;
  // RoomService.DeleteRoom was called
  ROOM_DELETED = 5;
  // the client is attempting to resume a session, but server is not aware of it
  STATE_MISMATCH = 6;
  // client was unable to connect fully
  JOIN_FAILURE = 7;
  // Cloud-only, the server requested Participant to migrate the connection elsewhere
  MIGRATION = 8;
  // the signal websocket was closed unexpectedly
  SIGNAL_CLOSE = 9;
  // the room was closed, due to all Standard and Ingress participants having left
  ROOM_CLOSED = 10;
  // SIP callee did not respond in time
  USER_UNAVAILABLE = 11;
  // SIP callee rejected the call (busy)
  USER_REJECTED = 12;
  // SIP protocol failure or unexpected response
  SIP_TRUNK_FAILURE = 13;
  CONNECTION_TIMEOUT = 14;
  MEDIA_FAILURE = 15;
}