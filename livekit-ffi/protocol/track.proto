// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package livekit.proto;
option csharp_namespace = "LiveKit.Proto";

import "e2ee.proto";
import "handle.proto";
import "stats.proto";

// Create a new VideoTrack from a VideoSource
message CreateVideoTrackRequest {
  required string name = 1;
  required uint64 source_handle = 2;
}
message CreateVideoTrackResponse {
  required OwnedTrack track = 1;
}

// Create a new AudioTrack from a AudioSource
message CreateAudioTrackRequest {
  required string name = 1;
  required uint64 source_handle = 2;
}
message CreateAudioTrackResponse {
  required OwnedTrack track = 1;
}

message GetStatsRequest {
  required uint64 track_handle = 1;
}
message GetStatsResponse {
  required uint64 async_id = 1;
}
message GetStatsCallback {
  required uint64 async_id = 1;
  optional string error = 2;
  repeated RtcStats stats = 3;
}

//
// Track
//

message TrackEvent {}

enum TrackKind {
  KIND_UNKNOWN = 0;
  KIND_AUDIO = 1;
  KIND_VIDEO = 2;
}

enum TrackSource {
  SOURCE_UNKNOWN = 0;
  SOURCE_CAMERA = 1;
  SOURCE_MICROPHONE = 2;
  SOURCE_SCREENSHARE = 3;
  SOURCE_SCREENSHARE_AUDIO = 4;
}

enum StreamState {
  STATE_UNKNOWN = 0;
  STATE_ACTIVE = 1;
  STATE_PAUSED = 2;
}

message TrackPublicationInfo {
  required string sid = 1;
  required string name = 2;
  required TrackKind kind = 3;
  required TrackSource source = 4;
  required bool simulcasted = 5;
  required uint32 width = 6;
  required uint32 height = 7;
  required string mime_type = 8;
  required bool muted = 9;
  required bool remote = 10;
  required EncryptionType encryption_type = 11;
  repeated AudioTrackFeature audio_features = 12;
}

message OwnedTrackPublication {
  required FfiOwnedHandle handle = 1;
  required TrackPublicationInfo info = 2;
}

message TrackInfo {
  required string sid = 1;
  required string name = 2;
  required TrackKind kind = 3;
  required StreamState stream_state = 4;
  required bool muted = 5;
  required bool remote = 6;
}

message OwnedTrack {
  required FfiOwnedHandle handle = 1;
  required TrackInfo info = 2;
}

// Mute/UnMute a track
message LocalTrackMuteRequest {
  required uint64 track_handle = 1;
  required bool mute = 2;
}

message LocalTrackMuteResponse {
  required bool muted = 1;
}

// Enable/Disable a remote track
message EnableRemoteTrackRequest {
  required uint64 track_handle = 1;
  required bool enabled = 2;
}

message EnableRemoteTrackResponse {
  required bool enabled = 1;
}

message SetTrackSubscriptionPermissionsRequest {
  required uint64 local_participant_handle = 1;
  required bool all_participants_allowed = 2;
  repeated ParticipantTrackPermission permissions = 3;
}

message ParticipantTrackPermission {
  // The participant identity this permission applies to.
  required string participant_identity = 1;
  // Grant permission to all all tracks. Takes precedence over allowedTrackSids.
  optional bool allow_all = 2;
  // List of track sids to grant permission to.
  repeated string allowed_track_sids = 3;
}

message SetTrackSubscriptionPermissionsResponse {
}

enum AudioTrackFeature {
  TF_STEREO = 0;
  TF_NO_DTX = 1;
  TF_AUTO_GAIN_CONTROL = 2;
  TF_ECHO_CANCELLATION = 3;
  TF_NOISE_SUPPRESSION = 4;
  TF_ENHANCED_NOISE_CANCELLATION = 5;
  TF_PRECONNECT_BUFFER = 6; // client will buffer audio once available and send it to the server via bytes stream once connected
}
