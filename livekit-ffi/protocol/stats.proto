// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package livekit.proto;
option csharp_namespace = "LiveKit.Proto";


enum DataChannelState {
    DC_CONNECTING = 0;
    DC_OPEN = 1;
    DC_CLOSING = 2;
    DC_CLOSED = 3;
}

enum QualityLimitationReason {
    LIMITATION_NONE = 0;
    LIMITATION_CPU = 1;
    LIMITATION_BANDWIDTH = 2;
    LIMITATION_OTHER = 3;
}

enum IceRole {
    ICE_UNKNOWN = 0;
    ICE_CONTROLLING = 1;
    ICE_CONTROLLED = 2;
}

enum DtlsTransportState {
    DTLS_TRANSPORT_NEW = 0;
    DTLS_TRANSPORT_CONNECTING = 1;
    DTLS_TRANSPORT_CONNECTED = 2;
    DTLS_TRANSPORT_CLOSED = 3;
    DTLS_TRANSPORT_FAILED = 4;
}

enum IceTransportState {
    ICE_TRANSPORT_NEW = 0;
    ICE_TRANSPORT_CHECKING = 1;
    ICE_TRANSPORT_CONNECTED = 2;
    ICE_TRANSPORT_COMPLETED = 3;
    ICE_TRANSPORT_DISCONNECTED = 4;
    ICE_TRANSPORT_FAILED = 5;
    ICE_TRANSPORT_CLOSED = 6;
}

enum DtlsRole {
    DTLS_CLIENT = 0;
    DTLS_SERVER = 1;
    DTLS_UNKNOWN = 2;
}

enum IceCandidatePairState {
    PAIR_FROZEN = 0;
    PAIR_WAITING = 1;
    PAIR_IN_PROGRESS = 2;
    PAIR_FAILED = 3;
    PAIR_SUCCEEDED = 4;
}

enum IceCandidateType {
    HOST = 0;
    SRFLX = 1;
    PRFLX = 2;
    RELAY = 3;
}

enum IceServerTransportProtocol {
    TRANSPORT_UDP = 0;
    TRANSPORT_TCP = 1;
    TRANSPORT_TLS = 2;
}

enum IceTcpCandidateType {
    CANDIDATE_ACTIVE = 0;
    CANDIDATE_PASSIVE = 1;
    CANDIDATE_SO = 2;
}

message RtcStats {
    message Codec {
        required RtcStatsData rtc = 1;
    	required CodecStats codec = 2;
    }

    message InboundRtp {
        required RtcStatsData rtc = 1;
        required RtpStreamStats stream = 2;
        required ReceivedRtpStreamStats received = 3;
        required InboundRtpStreamStats inbound = 4;
    }

    message OutboundRtp {
        required RtcStatsData rtc = 1;
        required RtpStreamStats stream = 2;
        required SentRtpStreamStats sent = 3;
        required OutboundRtpStreamStats outbound = 4;
    }

    message RemoteInboundRtp {
        required RtcStatsData rtc = 1;
        required RtpStreamStats stream = 2;
        required ReceivedRtpStreamStats received = 3;
        required RemoteInboundRtpStreamStats remote_inbound = 4;
    }

    message RemoteOutboundRtp {
        required RtcStatsData rtc = 1;
        required RtpStreamStats stream = 2;
        required SentRtpStreamStats sent = 3;
        required RemoteOutboundRtpStreamStats remote_outbound = 4;
    }

    message MediaSource {
        required RtcStatsData rtc = 1;
        required MediaSourceStats source = 2;
        required AudioSourceStats audio = 3;
        required VideoSourceStats video = 4;
    }

    message MediaPlayout {
        required RtcStatsData rtc = 1;
        required AudioPlayoutStats audio_playout = 2;
    }

    message PeerConnection {
        required RtcStatsData rtc = 1;
	    required PeerConnectionStats pc = 2;
    }

    message DataChannel {
        required RtcStatsData rtc = 1;
	    required DataChannelStats dc = 2;
    }

    message Transport {
        required RtcStatsData rtc = 1;
	    required TransportStats transport  = 2;
    }

    message CandidatePair {
        required RtcStatsData rtc = 1;
	    required CandidatePairStats candidate_pair = 2;
    }

    message LocalCandidate {
        required RtcStatsData rtc = 1;
	required IceCandidateStats candidate = 2;
    }

    message RemoteCandidate {
        required RtcStatsData rtc = 1;
	    required IceCandidateStats candidate = 2;
    }

    message Certificate {
        required RtcStatsData rtc = 1;
	    required CertificateStats certificate = 2;
    }

    message Stream {
        required RtcStatsData rtc = 1;
	    required StreamStats stream = 2;
    }

    message Track {
	// Deprecated
    }

    oneof stats {
	Codec codec = 3;
	InboundRtp inbound_rtp = 4;
	OutboundRtp outbound_rtp = 5;
	RemoteInboundRtp remote_inbound_rtp = 6;
	RemoteOutboundRtp remote_outbound_rtp = 7;
	MediaSource media_source = 8;
	MediaPlayout media_playout = 9;
	PeerConnection peer_connection = 10;
	DataChannel data_channel = 11;
	Transport transport = 12;
	CandidatePair candidate_pair = 13;
	LocalCandidate local_candidate = 14;
	RemoteCandidate remote_candidate = 15;
	Certificate certificate = 16;
    Stream stream = 17;
	Track track = 18;
    }
}

message RtcStatsData {
    required string id = 1;
    required int64 timestamp = 2;
}

message CodecStats {
    required uint32 payload_type = 1;
    required string transport_id = 2;
    required string mime_type = 3;
    required uint32 clock_rate = 4;
    required uint32 channels = 5;
    required string sdp_fmtp_line = 6;
}

message RtpStreamStats {
    required uint32 ssrc = 1;
    required string kind = 2;
    required string transport_id = 3;
    required string codec_id = 4;
}

message ReceivedRtpStreamStats {
    required uint64 packets_received = 1;
    required int64 packets_lost = 2;
    required double jitter = 3;
}

message InboundRtpStreamStats {
    required string track_identifier = 1;
    required string mid = 2;
    required string remote_id = 3;
    required uint32 frames_decoded = 4;
    required uint32 key_frames_decoded = 5;
    required uint32 frames_rendered = 6;
    required uint32 frames_dropped = 7;
    required uint32 frame_width = 8;
    required uint32 frame_height = 9;
    required double frames_per_second = 10;
    required uint64 qp_sum = 11;
    required double total_decode_time = 12;
    required double total_inter_frame_delay = 13;
    required double total_squared_inter_frame_delay = 14;
    required uint32 pause_count = 15;
    required double total_pause_duration = 16;
    required uint32 freeze_count = 17;
    required double total_freeze_duration = 18;
    required double last_packet_received_timestamp = 19;
    required uint64 header_bytes_received = 20;
    required uint64 packets_discarded = 21;
    required  uint64 fec_bytes_received = 22;
    required uint64 fec_packets_received = 23;
    required uint64 fec_packets_discarded = 24;
    required uint64 bytes_received = 25;
    required uint32 nack_count = 26;
    required uint32 fir_count = 27;
    required uint32 pli_count = 28;
    required double total_processing_delay = 29;
    required double estimated_playout_timestamp = 30;
    required double jitter_buffer_delay = 31;
    required double jitter_buffer_target_delay = 32;
    required uint64 jitter_buffer_emitted_count = 33;
    required double jitter_buffer_minimum_delay = 34;
    required uint64 total_samples_received = 35;
    required uint64 concealed_samples = 36;
    required uint64 silent_concealed_samples = 37;
    required uint64 concealment_events = 38;
    required uint64 inserted_samples_for_deceleration = 39;
    required uint64 removed_samples_for_acceleration = 40;
    required double audio_level = 41;
    required double total_audio_energy = 42;
    required double total_samples_duration = 43;
    required uint64 frames_received = 44;
    required string decoder_implementation = 45;
    required string playout_id = 46;
    required  bool power_efficient_decoder = 47;
    required uint64 frames_assembled_from_multiple_packets = 48;
    required double total_assembly_time = 49;
    required uint64 retransmitted_packets_received = 50;
    required  uint64 retransmitted_bytes_received = 51;
    required uint32 rtx_ssrc = 52;
    required uint32 fec_ssrc = 53;
}

message SentRtpStreamStats {
    required uint64 packets_sent = 1;
    required uint64 bytes_sent = 2;
}

message OutboundRtpStreamStats {
    required string mid = 1;
    required string media_source_id = 2;
    required string remote_id = 3;
    required string rid = 4;
    required uint64 header_bytes_sent = 5;
    required uint64 retransmitted_packets_sent = 6;
    required uint64 retransmitted_bytes_sent = 7;
    required uint32 rtx_ssrc = 8;
    required double target_bitrate = 9;
    required uint64 total_encoded_bytes_target = 10;
    required uint32 frame_width = 11;
    required uint32 frame_height = 12;
    required double frames_per_second = 13;
    required uint32 frames_sent = 14;
    required uint32 huge_frames_sent = 15;
    required uint32 frames_encoded = 16;
    required uint32 key_frames_encoded = 17;
    required uint64 qp_sum = 18;
    required double total_encode_time = 19;
    required double total_packet_send_delay = 20;
    required QualityLimitationReason quality_limitation_reason = 21;
    map<string, double> quality_limitation_durations = 22;
    required uint32 quality_limitation_resolution_changes = 23;
    required uint32 nack_count = 24;
    required uint32 fir_count = 25;
    required uint32 pli_count = 26;
    required string encoder_implementation = 27;
    required bool power_efficient_encoder = 28;
    required bool active = 29;
    required string scalability_mode = 30;
}

message RemoteInboundRtpStreamStats {
    required string local_id = 1;
    required double round_trip_time = 2;
    required double total_round_trip_time = 3;
    required double fraction_lost = 4;
    required uint64 round_trip_time_measurements = 5;
}

message RemoteOutboundRtpStreamStats {
    required string local_id = 1;
    required double remote_timestamp = 2;
    required uint64 reports_sent = 3;
    required double round_trip_time = 4;
    required double total_round_trip_time = 5;
    required uint64 round_trip_time_measurements = 6;
}

message MediaSourceStats {
    required string track_identifier = 1;
    required string kind = 2;
}

message AudioSourceStats {
    required double audio_level = 1;
    required double total_audio_energy = 2;
    required double total_samples_duration = 3;
    required double echo_return_loss = 4;
    required double echo_return_loss_enhancement = 5;
    required double dropped_samples_duration = 6;
    required uint32 dropped_samples_events = 7;
    required double total_capture_delay = 8;
    required uint64 total_samples_captured = 9;
}

message VideoSourceStats {
    required uint32 width = 1;
    required uint32 height = 2;
    required uint32 frames = 3;
    required double frames_per_second = 4;
}

message AudioPlayoutStats {
    required string kind = 1;
    required double synthesized_samples_duration = 2;
    required uint32 synthesized_samples_events = 3;
    required double total_samples_duration = 4;
    required double total_playout_delay = 5;
    required uint64 total_samples_count = 6;
}

message PeerConnectionStats {
    required uint32 data_channels_opened = 1;
    required uint32 data_channels_closed = 2;
}

message DataChannelStats {
    required string label = 1;
    required string protocol = 2;
    required int32 data_channel_identifier = 3;
    optional DataChannelState state = 4;
    required uint32 messages_sent = 5;
    required uint64 bytes_sent = 6;
    required uint32 messages_received = 7;
    required uint64 bytes_received = 8;
}

message TransportStats {
    required uint64 packets_sent = 1;
    required uint64 packets_received = 2;
    required uint64 bytes_sent = 3;
    required uint64 bytes_received = 4;
    required IceRole ice_role = 5;
    required string ice_local_username_fragment = 6;
    optional DtlsTransportState dtls_state = 7;
    optional IceTransportState ice_state = 8;
    required string selected_candidate_pair_id = 9;
    required string local_certificate_id = 10;
    required string remote_certificate_id = 11;
    required string tls_version = 12;
    required string dtls_cipher = 13;
    required DtlsRole dtls_role = 14;
    required string srtp_cipher = 15;
    required uint32 selected_candidate_pair_changes = 16;
}

message CandidatePairStats {
    required string transport_id = 1;
    required string local_candidate_id = 2;
    required string remote_candidate_id = 3;
    optional IceCandidatePairState state = 4;
    required bool nominated = 5;
    required uint64 packets_sent = 6;
    required uint64 packets_received = 7;
    required uint64 bytes_sent = 8;
    required uint64 bytes_received = 9;
    required double last_packet_sent_timestamp = 10;
    required double last_packet_received_timestamp = 11;
    required double total_round_trip_time = 12;
    required double current_round_trip_time = 13;
    required double available_outgoing_bitrate = 14;
    required double available_incoming_bitrate = 15;
    required uint64 requests_received = 16;
    required uint64 requests_sent = 17;
    required uint64 responses_received = 18;
    required uint64 responses_sent = 19;
    required uint64 consent_requests_sent = 20;
    required uint32 packets_discarded_on_send = 21;
    required uint64 bytes_discarded_on_send = 22;
}

message IceCandidateStats {
    required string transport_id = 1;
    required string address = 2;
    required int32 port = 3;
    required string protocol = 4;
    optional IceCandidateType candidate_type = 5;
    required int32 priority = 6;
    required string url = 7;
    optional IceServerTransportProtocol relay_protocol = 8;
    required string foundation = 9;
    required string related_address = 10;
    required int32 related_port = 11;
    required string username_fragment = 12;
    optional IceTcpCandidateType tcp_type = 13;
}

message CertificateStats {
    required string fingerprint = 1;
    required string fingerprint_algorithm = 2;
    required string base64_certificate = 3;
    required string issuer_certificate_id = 4;
}

message StreamStats {
    required string id = 1;
    required string stream_identifier = 2;
    // required int64 timestamp = 3;
}

