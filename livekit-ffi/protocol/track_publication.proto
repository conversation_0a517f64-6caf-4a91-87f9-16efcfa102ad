// Copyright 2023 LiveKit, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package livekit.proto;
option csharp_namespace = "LiveKit.Proto";

// Enable/Disable a remote track publication
message EnableRemoteTrackPublicationRequest {
  required uint64 track_publication_handle = 1;
  required bool enabled = 2;
}

message EnableRemoteTrackPublicationResponse {}

// update a remote track publication dimension
message UpdateRemoteTrackPublicationDimensionRequest {
  required uint64 track_publication_handle = 1;
  required uint32 width = 2;
  required uint32 height = 3;
}

message UpdateRemoteTrackPublicationDimensionResponse {}



