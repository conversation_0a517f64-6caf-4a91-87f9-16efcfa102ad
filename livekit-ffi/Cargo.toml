[package]
name = "livekit-ffi"
version = "0.12.28"
edition = "2021"
license = "Apache-2.0"
description = "FFI interface for bindings in other languages"
repository = "https://github.com/livekit/rust-sdks"

[features]
default = ["rustls-tls-native-roots"]
native-tls = ["livekit/native-tls"]
native-tls-vendored = ["livekit/native-tls-vendored"]
rustls-tls-native-roots = ["livekit/rustls-tls-native-roots"]
rustls-tls-webpki-roots = ["livekit/rustls-tls-webpki-roots"]
__rustls-tls = ["livekit/__rustls-tls"]

# Enable tokio-console to debug tasks
tracing = ["tokio/tracing", "console-subscriber"]

[dependencies]
livekit = { workspace = true }
soxr-sys = { workspace = true }
imgproc = { workspace = true }
livekit-protocol = { workspace = true }
tokio = { version = "1", features = ["full", "parking_lot"] }
futures-util = { version = "0.3", default-features = false, features = ["sink"] }
parking_lot = { version = "0.12", features = ["deadlock_detection"] }
prost = "0.12"
prost-types = "0.12"
lazy_static = "1.4"
thiserror = "1.0"
log = "0.4"
dashmap = "5.4"
env_logger = "0.10"
downcast-rs = "1.2"
console-subscriber = { version = "0.1", features = ["parking_lot"], optional = true }
bytes = "1.10.1"

[target.'cfg(target_os = "android")'.dependencies]
jni = "0.21.1"

[build-dependencies]
webrtc-sys-build = { workspace = true }

[dev-dependencies]
livekit-api = { workspace = true }

[lib]
crate-type = ["lib", "cdylib"]
