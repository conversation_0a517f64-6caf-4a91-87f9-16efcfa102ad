/* SoX Resampler Library      Copyright (c) 2007-16 <EMAIL>
 * Licence for this file: LGPL v2.1                  See LICENCE for details. */

#if defined SOXR_LIB

#define lsx_bessel_I_0                 _soxr_bessel_I_0
#define lsx_cdft_f                     _soxr_cdft_f
#define lsx_cdft                       _soxr_cdft
#define lsx_clear_fft_cache_f          _soxr_clear_fft_cache_f
#define lsx_clear_fft_cache            _soxr_clear_fft_cache
#define lsx_ddct_f                     _soxr_ddct_f
#define lsx_ddct                       _soxr_ddct
#define lsx_ddst_f                     _soxr_ddst_f
#define lsx_ddst                       _soxr_ddst
#define lsx_design_lpf                 _soxr_design_lpf
#define lsx_dfct_f                     _soxr_dfct_f
#define lsx_dfct                       _soxr_dfct
#define lsx_dfst_f                     _soxr_dfst_f
#define lsx_dfst                       _soxr_dfst
#define lsx_fir_to_phase               _soxr_fir_to_phase
#define lsx_f_resp                     _soxr_f_resp
#define lsx_init_fft_cache_f           _soxr_init_fft_cache_f
#define lsx_init_fft_cache             _soxr_init_fft_cache
#define lsx_inv_f_resp                 _soxr_inv_f_resp
#define lsx_kaiser_beta                _soxr_kaiser_beta
#define lsx_kaiser_params              _soxr_kaiser_params
#define lsx_make_lpf                   _soxr_make_lpf
#define lsx_ordered_convolve_f         _soxr_ordered_convolve_f
#define lsx_ordered_convolve           _soxr_ordered_convolve
#define lsx_ordered_partial_convolve_f _soxr_ordered_partial_convolve_f
#define lsx_ordered_partial_convolve   _soxr_ordered_partial_convolve
#define lsx_rdft_f                     _soxr_rdft_f
#define lsx_rdft                       _soxr_rdft
#define lsx_safe_cdft_f                _soxr_safe_cdft_f
#define lsx_safe_cdft                  _soxr_safe_cdft
#define lsx_safe_rdft_f                _soxr_safe_rdft_f
#define lsx_safe_rdft                  _soxr_safe_rdft

#endif
