/* SoX Resampler Library      Copyright (c) 2007-13 <EMAIL>
 * Licence for this file: LGPL v2.1                  See LICENCE for details. */

#if defined __GNUC__
  #pragma GCC system_header
#elif defined __SUNPRO_C
  #pragma disable_warn
#elif defined _MSC_VER
  #pragma warning(push, 1)
#endif

#if CORE_TYPE & CORE_SIMD_HALF
  #define VALIGN vAlign
#else
  #define VALIGN
#endif

#if !(CORE_TYPE & CORE_SIMD_HALF)
static VALIGN const sample_t half_fir_coefs_7[] = {
 3.1062656496657370e-01, -8.4998810699955796e-02,  3.4007044621123500e-02,
-1.2839903789829387e-02,  3.9899380181723145e-03, -8.9355202017945374e-04,
 1.0918292424806546e-04,
};
#endif

static VALIGN const sample_t half_fir_coefs_8[] = {
 3.1154652365332069e-01, -8.7344917685739543e-02,  3.6814458353637280e-02,
-1.5189204581464479e-02,  5.4540855610738801e-03, -1.5643862626630416e-03,
 3.1816575906323303e-04, -3.4799449225005688e-05,
};

static VALIGN const sample_t half_fir_coefs_9[] = {
 3.1227034755311189e-01, -8.9221517147969526e-02,  3.9139704015071934e-02,
-1.7250558515852023e-02,  6.8589440230476112e-03, -2.3045049636430419e-03,
 6.0963740543348963e-04, -1.1323803957431231e-04,  1.1197769991000046e-05,
};

#if CORE_TYPE & CORE_DBL
static VALIGN const sample_t half_fir_coefs_10[] = {
 3.1285456012000523e-01, -9.0756740799292787e-02,  4.1096398104193160e-02,
-1.9066319572525220e-02,  8.1840569787684902e-03, -3.0766876176359834e-03,
 9.6396524429277980e-04, -2.3585679989922018e-04,  4.0252189026627833e-05,
-3.6298196342497932e-06,
};

static VALIGN const sample_t half_fir_coefs_11[] = {
 3.1333588822574199e-01, -9.2035898673019811e-02,  4.2765169698406408e-02,
-2.0673580894964429e-02,  9.4225426824512421e-03, -3.8563379950013192e-03,
 1.3634742159642453e-03, -3.9874150714431009e-04,  9.0586723632664806e-05,
-1.4285617244076783e-05,  1.1834642946400529e-06,
};

static VALIGN const sample_t half_fir_coefs_12[] = {
 3.1373928463345568e-01, -9.3118180335301962e-02,  4.4205005881659098e-02,
-2.2103860986973051e-02,  1.0574689371162864e-02, -4.6276428065385065e-03,
 1.7936153397572132e-03, -5.9617527051353237e-04,  1.6314517495669067e-04,
-3.4555126770115446e-05,  5.0617615610782593e-06, -3.8768958592971409e-07,
};

static VALIGN const sample_t half_fir_coefs_13[] = {
 3.1408224847888910e-01, -9.4045836332667387e-02,  4.5459878763259978e-02,
-2.3383369012219993e-02,  1.1644273044890753e-02, -5.3806714579057013e-03,
 2.2429072878264022e-03, -8.2204347506606424e-04,  2.5724946477840893e-04,
-6.6072709864248668e-05,  1.3099163296288644e-05, -1.7907147069136000e-06,
 1.2750825595240592e-07,
};
#endif

#undef VALIGN

#if defined __SUNPRO_C
  #pragma enable_warn
#elif defined _MSC_VER
  #pragma warning(pop)
#endif
