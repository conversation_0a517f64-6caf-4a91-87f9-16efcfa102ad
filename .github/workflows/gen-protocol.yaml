# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Generate protocol

on:
  pull_request:
    branches: ["main"]
    paths:
      - livekit-protocol/**

env:
  CARGO_TERM_COLOR: always
  PACKAGE_DIR: ./livekit-protocol

jobs:
  generate_protobuf:
    runs-on: ubuntu-latest
    name: Generating protobuf

    defaults:
      run:
        working-directory: ${{ env.PACKAGE_DIR }}

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          ref: ${{ github.event.pull_request.head.ref }}

      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable

      - name: Install Protoc
        uses: arduino/setup-protoc@v2
        with:
          version: "25.2"
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Install prost generators
        run: cargo install protoc-gen-prost@0.3.1 protoc-gen-prost-serde@0.3.1

      - name: generate python stubs
        run: ./generate_proto.sh

      - name: Add changes
        uses: EndBug/add-and-commit@v9
        with:
          add: '["livekit-protocol/"]'
          default_author: github_actions
          message: generated protobuf
