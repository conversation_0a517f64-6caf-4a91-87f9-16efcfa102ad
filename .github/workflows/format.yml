name: Rust Formatting

on:
  workflow_dispatch:
  push:
    branches: [main]
  pull_request:
    branches: [main]
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review

env:
  CARGO_TERM_COLOR: always

jobs:
  formatting:
    name: Check Formatting
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || !github.event.pull_request.draft

    steps:
      - name: Checkout the repo
        uses: actions/checkout@v3

      - name: Install Rust
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: nightly-2023-12-30
          components: rustfmt

      - name: Check workspace versions match
        run: |
          cargo metadata --format-version=1 --no-deps |
          jq -e -r '.packages[] | "\(.name) \(.version)"' |
          sort | xargs -l bash -c \
          'grep "$0 = { version = \"$1\"" -q Cargo.toml && printf "version %-7s %-27s $0\n" $1 "matches package" || printf "version %-7s is not reflected on package $0\n" "$1" | false';
          if [ $? -eq 0 ]; then exit 0; else exit 1; fi

      - name: Cargo fmt
        run: |
          cargo fmt -- --check
