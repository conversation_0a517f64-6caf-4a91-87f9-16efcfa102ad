# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: WebRTC builds
on:
  push:
    tags:
      - "webrtc-*"
  workflow_dispatch:

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        target:
          - name: win
            os: windows-latest
            cmd: .\build_windows.cmd
            arch: x64

          - name: win
            os: windows-latest
            cmd: .\build_windows.cmd
            arch: arm64

          - name: mac
            os: macos-latest
            cmd: ./build_macos.sh
            arch: x64

          - name: mac
            os: macos-latest
            cmd: ./build_macos.sh
            arch: arm64

          - name: linux
            os: ubuntu-latest
            cmd: ./build_linux.sh
            arch: x64

          - name: linux
            os: ubuntu-latest
            cmd: ./build_linux.sh
            arch: arm64

          - name: android
            os: ubuntu-latest
            cmd: ./build_android.sh
            arch: arm64

          - name: android
            os: ubuntu-latest
            cmd: ./build_android.sh
            arch: arm

          - name: android
            os: ubuntu-latest
            cmd: ./build_android.sh
            arch: x64

          - name: ios
            out: ios-device-arm64
            os: macos-latest
            cmd: ./build_ios.sh
            arch: arm64

          - name: ios
            out: ios-simulator-arm64
            os: macos-latest
            cmd: ./build_ios.sh
            arch: arm64
            buildargs: --environment simulator
        profile:
          - release
    #          - debug

    name: Build webrtc (${{ matrix.target.name }}-${{ matrix.target.arch }}-${{ matrix.profile }}) ${{ matrix.target.buildargs }}
    runs-on: ${{ matrix.target.os }}
    steps:
      - name: Setup variables
        id: setup
        run: |
          DEFAULT_OUT=${{ matrix.target.name }}-${{ matrix.target.arch }}
          OUT=${{ matrix.target.out == '' && '$DEFAULT_OUT' || matrix.target.out }}-${{ matrix.profile }}
          echo OUT=$OUT >> $GITHUB_OUTPUT
          echo ZIP=webrtc-$OUT.zip >> $GITHUB_OUTPUT
        shell: bash

      # Print some debug infos to be sure everything is ok before doing really long tasks..
      - name: Info
        run: |
          echo "OutName: ${{ steps.setup.outputs.OUT }}"
          echo "OutZip: ${{ steps.setup.outputs.ZIP }}"

      - uses: actions/checkout@v4
        with:
          submodules: true

      - uses: actions/setup-python@v4

      - name: install setuptools (none-macOS)
        if: ${{ matrix.target.os != 'macos-latest' }}
        run: |
         pip3 install setuptools # pkg_resources is sometimes not found?

      - name: Install linux dependencies
        if: ${{ matrix.target.os == 'ubuntu-latest' }}
        run: |
          sudo apt update -y
          sudo apt install -y ninja-build pkg-config openjdk-11-jdk

      - name: Install macos dependencies
        if: ${{ matrix.target.os == 'macos-latest' }}
        run: brew install ninja

      - name: Install windows dependencies
        if: ${{ matrix.target.os == 'windows-latest' }}
        run: |
          Invoke-WebRequest -Uri "https://github.com/ninja-build/ninja/releases/latest/download/ninja-win.zip" -OutFile ninja.zip
          Expand-Archive -Path ninja.zip -DestinationPath ${{ github.workspace }}\ninja
          echo "${{ github.workspace }}\ninja" | Out-File -Append -Encoding utf8 -FilePath $Env:GITHUB_PATH

      - name: Print ninja version
        run: ninja --version

      - name: Target OS
        run: echo -e "\ntarget_os = [\"${{ matrix.target.name }}\"]" >> .gclient
        shell: bash
        working-directory: webrtc-sys/libwebrtc

      - name: Build WebRTC
        run: ${{ matrix.target.cmd }} --arch ${{ matrix.target.arch }} --profile ${{ matrix.profile }} ${{ matrix.target.buildargs }}
        working-directory: webrtc-sys/libwebrtc

      - name: Zip artifact (Unix)
        if: ${{ matrix.target.os != 'windows-latest' }}
        run: |
          cd webrtc-sys/libwebrtc
          zip ${{ github.workspace }}/${{ steps.setup.outputs.ZIP }} ${{ steps.setup.outputs.OUT }} -r

      - name: Zip artifact (Windows)
        if: ${{ matrix.target.os == 'windows-latest' }}
        run: Compress-Archive -Path .\webrtc-sys\libwebrtc\${{ steps.setup.outputs.OUT }} -DestinationPath ${{ steps.setup.outputs.ZIP }}

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: webrtc-builds-${{ steps.setup.outputs.OUT }}
          path: ${{ steps.setup.outputs.ZIP }}

  release:
    name: Release to GH (Draft)
    runs-on: ubuntu-latest
    needs: build
    permissions:
      contents: write
    if: startsWith(github.ref, 'refs/tags/webrtc-')
    env:
      GH_TOKEN: ${{ github.token }}
    steps:
      - uses: actions/checkout@v4

      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: webrtc-builds-*
          merge-multiple: true
          path: ${{ github.workspace }}/webrtc-builds

      - name: Create draft release
        run: |
          gh release create ${{ github.ref_name }} --draft --title "${{ github.ref_name }}"
          gh release upload ${{ github.ref_name }} ${{ github.workspace }}/webrtc-builds/*
