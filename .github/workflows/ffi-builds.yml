# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: FFI
on:
  push:
    branches: ["main"]
    tags:
      - "rust-sdks/livekit-ffi@*"
  workflow_dispatch:

env:
  CARGO_TERM_COLOR: always

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: windows-latest
            platform: windows
            dylib: livekit_ffi.dll
            target: x86_64-pc-windows-msvc
            name: ffi-windows-x86_64
          - os: windows-latest
            platform: windows
            dylib: livekit_ffi.dll
            buildargs: --no-default-features --features "native-tls" # ring 0.16 is incompatible with win aarch64
            target: aarch64-pc-windows-msvc
            name: ffi-windows-arm64
          - os: macos-latest
            platform: macos
            dylib: liblivekit_ffi.dylib
            target: x86_64-apple-darwin
            macosx_deployment_target: "10.15"
            name: ffi-macos-x86_64
          - os: macos-latest
            platform: macos
            dylib: liblivekit_ffi.dylib
            target: aarch64-apple-darwin
            macosx_deployment_target: "11.0" # aarch64 requires 11
            name: ffi-macos-arm64
          - os: macos-latest
            platform: ios
            dylib: liblivekit_ffi.a
            target: aarch64-apple-ios
            iphoneos_deployment_target: "13.0"
            name: ffi-ios-arm64
            buildargs: --no-default-features --features "rustls-tls-webpki-roots"
          - os: macos-latest
            platform: ios
            dylib: liblivekit_ffi.a
            target: aarch64-apple-ios-sim
            iphoneos_deployment_target: "13.0"
            name: ffi-ios-sim-arm64
            buildargs: --no-default-features --features "rustls-tls-webpki-roots"
          - os: ubuntu-latest
            platform: linux
            build_image: quay.io/pypa/manylinux_2_28_x86_64
            dylib: liblivekit_ffi.so
            target: x86_64-unknown-linux-gnu
            name: ffi-linux-x86_64
          - os: ubuntu-24.04-arm
            platform: linux
            build_image: quay.io/pypa/manylinux_2_28_aarch64
            dylib: liblivekit_ffi.so
            target: aarch64-unknown-linux-gnu
            name: ffi-linux-arm64
          - os: ubuntu-latest
            platform: android
            dylib: liblivekit_ffi.so
            jar: libwebrtc.jar
            target: aarch64-linux-android
            ndk_arch: aarch64-unknown-linux-musl
            name: ffi-android-arm64
            buildargs: --no-default-features --features "rustls-tls-webpki-roots"
          - os: ubuntu-latest
            platform: android
            dylib: liblivekit_ffi.so
            jar: libwebrtc.jar
            target: armv7-linux-androideabi
            ndk_arch: arm-unknown-linux-musleabihf
            name: ffi-android-armv7
            buildargs: --no-default-features --features "rustls-tls-webpki-roots"
          - os: ubuntu-latest
            platform: android
            dylib: liblivekit_ffi.so
            jar: libwebrtc.jar
            target: x86_64-linux-android
            ndk_arch: x86_64-unknown-linux-musl
            name: ffi-android-x86_64
            buildargs: --no-default-features --features "rustls-tls-webpki-roots"

    name: Build (${{ matrix.target }})
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true

      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          target: ${{ matrix.target }}

      - name: Set up QEMU
        if: ${{ matrix.target == 'aarch64-unknown-linux-gnu' }}
        uses: docker/setup-qemu-action@v3

      - name: Build (macOS)
        if: ${{ matrix.platform == 'macos' }}
        env:
          MACOSX_DEPLOYMENT_TARGET: ${{ matrix.macosx_deployment_target }}
        run: cd livekit-ffi && cargo build --release --target ${{ matrix.target }} ${{ matrix.buildargs }}

      - name: Build (iOS)
        if: ${{ matrix.platform == 'ios' }}
        # TODO(theomonnom): Seems like this is causing issues with the linker (let’s ignore for now)
        #env:
        #  IPHONEOS_DEPLOYMENT_TARGET: ${{ matrix.iphoneos_deployment_target }}
        # run: cd livekit-ffi && cargo build --release --target ${{ matrix.target }} ${{ matrix.buildargs }}
        run: cd livekit-ffi && cargo rustc --crate-type staticlib --release --target ${{ matrix.target }} ${{ matrix.buildargs }}

      - name: Build (Windows)
        if: ${{ matrix.platform == 'windows' }}
        run: cd livekit-ffi && cargo build --release --target ${{ matrix.target }} ${{ matrix.buildargs }}

      # Use Docker on linux, so we can use manylinux images (ensure maximum mcompatibility)
      - name: Build (Linux)
        if: ${{ matrix.platform == 'linux' }}
        run: |
          docker run --rm -v $PWD:/workspace -w /workspace ${{ matrix.build_image }} bash -c "\
            uname -a; \
            export PATH=/root/.cargo/bin:\$PATH; \
            yum install llvm llvm-libs -y; \
            yum install clang -y; \
            yum groupinstall 'Development Tools' -y; \
            clang --version; \
            yum install openssl-devel libX11-devel mesa-libGL-devel libXext-devel -y; \
            curl --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y; \
            cd livekit-ffi && cargo build --release --target ${{ matrix.target }} ${{ matrix.buildargs }}"

          sudo chown -R $USER:$USER target/${{ matrix.target }}/release/

      # on android use cargo ndk
      - name: Build (Android)
        if: ${{ matrix.platform == 'android' }}
        run: |
          cd livekit-ffi/
          ln -sf $ANDROID_NDK_ROOT/toolchains/llvm/prebuilt/linux-x86_64/lib/${{ matrix.ndk_arch }}/{libunwind.so,libc++abi.a} $ANDROID_NDK_ROOT/toolchains/llvm/prebuilt/linux-x86_64/lib/
          cargo install cargo-ndk
          cargo ndk --bindgen --target ${{ matrix.target }} build --release ${{ matrix.buildargs }}

      - name: Copy/Build licenses
        run: |
          echo "# livekit" > TEMP_LICENSE.md
          echo "```" >> TEMP_LICENSE.md
          cat LICENSE >> TEMP_LICENSE.md
          echo "```" >> TEMP_LICENSE.md
          cat livekit-ffi/WEBRTC_LICENSE.md >> TEMP_LICENSE.md
          mv TEMP_LICENSE.md target/${{ matrix.target }}/release/LICENSE.md
        shell: bash

      # zip the files
      - name: Zip artifact (Unix)
        if: ${{ matrix.os != 'windows-latest' && matrix.platform != 'android'}}
        run: |
          cp livekit-ffi/include/livekit_ffi.h target/${{ matrix.target }}/release/ 
          cd target/${{ matrix.target }}/release/
          zip ${{ github.workspace }}/${{ matrix.name }}.zip ${{ matrix.dylib }} livekit_ffi.h LICENSE.md

      - name: Zip artifact (Unix for Android)
        if: ${{ matrix.os != 'windows-latest' && matrix.platform == 'android'}}
        run: |
          cp livekit-ffi/include/livekit_ffi.h target/${{ matrix.target }}/release/ 
          cd target/${{ matrix.target }}/release/
          zip ${{ github.workspace }}/${{ matrix.name }}.zip ${{ matrix.dylib }} ${{ matrix.jar }} livekit_ffi.h LICENSE.md

      - name: Zip artifact (Windows)
        if: ${{ matrix.os == 'windows-latest' }}
        run: |
          cp livekit-ffi/include/livekit_ffi.h target/${{ matrix.target }}/release/
          cd target/${{ matrix.target }}/release/
          Get-ChildItem -Path ${{ matrix.dylib }}, livekit_ffi.h, LICENSE.md | Compress-Archive -DestinationPath ${{ github.workspace }}\${{ matrix.name }}.zip

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ffi-builds-${{ matrix.target }}
          path: ${{ matrix.name }}.zip

  release:
    name: Release to GH (Draft)
    runs-on: ubuntu-latest
    needs: build
    permissions:
      contents: write
    if: startsWith(github.ref, 'refs/tags/rust-sdks/livekit-ffi')
    env:
      GH_TOKEN: ${{ github.token }}
    steps:
      - uses: actions/checkout@v3

      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: ffi-builds-*
          merge-multiple: true
          path: ${{ github.workspace }}/ffi-builds

      - name: Create draft release
        run: |
          gh release create ${{ github.ref_name }} --draft --title "${{ github.ref_name }}"
          gh release upload ${{ github.ref_name }} ${{ github.workspace }}/ffi-builds/*
