# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Tests

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]

env:
  CARGO_TERM_COLOR: always
  RUST_LOG: debug

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        include:
          # Platform supports is limited for tests (no aarch64)
          - os: windows-latest
            target: x86_64-pc-windows-msvc
          - os: macos-latest
            target: x86_64-apple-darwin
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu

    name: Test (${{ matrix.target }})
    runs-on: ${{ matrix.os }}
    steps:
      - name: Install Rust toolchain
        run: |
          rustup update --no-self-update nightly
          rustup target add ${{ matrix.target }} --toolchain nightly

      - name: Install linux dependencies
        if: ${{ matrix.os == 'ubuntu-latest' }}
        run: |
          sudo apt update -y
          sudo apt install -y libssl-dev libx11-dev libgl1-mesa-dev libxext-dev

      - uses: actions/checkout@v3
        with:
          submodules: true

      - name: Test
        run: cargo +nightly test --release --verbose --target ${{ matrix.target }} -- --nocapture


