# Copyright 2023 LiveKit, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Builds

on:
  push:
    branches: ["main"]
    paths:
      - livekit/**
      - libwebrtc/**
  pull_request:
    branches: ["main"]
    paths:
      - livekit/**
      - libwebrtc/**

env:
  CARGO_TERM_COLOR: always

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: windows-latest
            target: x86_64-pc-windows-msvc
          - os: windows-latest
            target: aarch64-pc-windows-msvc
            #extraargs: --exclude livekit-api --exclude livekit-ffi # waiting for v0.17 of ring
          - os: macos-latest
            target: x86_64-apple-darwin
          - os: macos-latest
            target: aarch64-apple-darwin
          - os: macos-latest
            target: aarch64-apple-ios
          - os: macos-latest
            target: aarch64-apple-ios-sim
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
          - os: ubuntu-24.04-arm
            target: aarch64-unknown-linux-gnu
          - os: ubuntu-latest
            target: aarch64-linux-android
            ndk_arch: aarch64-unknown-linux-musl
          - os: ubuntu-latest
            target: armv7-linux-androideabi
            ndk_arch: arm-unknown-linux-musleabihf
          - os: ubuntu-latest
            target: x86_64-linux-android
            ndk_arch: x86_64-unknown-linux-musl

    name: Build (${{ matrix.target }})
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true

      - name: Install linux dependencies
        if: ${{ matrix.os == 'ubuntu-latest' || matrix.os == 'ubuntu-24.04-arm'}}
        run: |
          sudo apt update -y
          sudo apt install -y libssl-dev libx11-dev libgl1-mesa-dev libxext-dev

      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          target: ${{ matrix.target }}

      - name: Build (Cargo)
        if: ${{ !contains(matrix.target, 'android') }}
        run: |
          cargo build --release --workspace -p livekit --target ${{ matrix.target }}

      - name: Build (Android)
        if: ${{ contains(matrix.target, 'android') }}
        run: |
          ln -sf $ANDROID_NDK_ROOT/toolchains/llvm/prebuilt/linux-x86_64/lib/${{ matrix.ndk_arch }}/{libunwind.so,libc++abi.a} $ANDROID_NDK_ROOT/toolchains/llvm/prebuilt/linux-x86_64/lib/
          cargo install cargo-ndk
          cargo ndk --target ${{ matrix.target }} build --release -p livekit --workspace -vv
