[package]
name = "wgpu_room"
version = "0.1.1"
edition = "2021"

[features]
default = []
tracing = ["console-subscriber", "tokio/tracing"]

[dependencies]
tokio = { version = "1", features = ["full", "parking_lot"] }
livekit = { path = "../../livekit", features = ["native-tls"] }
futures = "0.3"
winit = "0.30.11"
parking_lot = { version = "0.12.1", features = ["deadlock_detection"] }
image = "0.24"
wgpu = "25.0"
egui = "0.31.1"
egui-wgpu = "0.31.1"
eframe = { version = "0.31.1", default-features = false, features = ["default_fonts", "wgpu", "persistence"] }
serde = { version = "1", features = ["derive"] }
log = "0.4"
env_logger = "0.10.0"
console-subscriber = { version = "0.1.10", features = ["parking_lot"], optional = true }
