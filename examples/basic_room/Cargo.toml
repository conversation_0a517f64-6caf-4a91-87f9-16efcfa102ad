[package]
name = "basic_room"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1", features = ["full"] }
smol = "2.0"
env_logger = "0.10"
livekit = { path = "../../livekit", features = ["rustls-tls-native-roots"]}
livekit-api = { path = "../../livekit-api", features = ["rustls-tls-native-roots"]}
log = "0.4"
futures = "0.3.0"
livekit-runtime = { path = "../../livekit-runtime", default-features = false, features = ["dispatcher"] }
dotenv = "0.15"
