[package]
name = "mobile"
version = "0.1.0"
edition = "2021"

[dependencies]
lazy_static = "1.4.0"
livekit = { path = "../../livekit", features = ["rustls-tls-webpki-roots"] }
log = "0.4.19"
tokio = { version = "1", features = ["full"] }

[target.'cfg(target_os = "android")'.dependencies]
jni = "0.21.1"
android_logger = "0.13.1"

[build-dependencies]
webrtc-sys-build = { path = "../../webrtc-sys/build" }

[lib]
crate-type = ["cdylib"]
