[package]
name = "basic_room_dispatcher"
version = "0.1.0"
edition = "2021"

[dependencies]
futures = "0.3.0"
smol = "2.0.0"
env_logger = "0.10"
livekit = { path = "../../livekit", default-features = false, features = ["native-tls", "dispatcher"] }
livekit-runtime = { path = "../../livekit-runtime", default-features = false, features = ["dispatcher"] }
livekit-api = { path = "../../livekit-api" }
log = "0.4"
dotenv = "0.15"

[workspace]
