[package]
name = "livekit-api"
version = "0.4.4"
license = "Apache-2.0"
description = "Rust Server SDK for LiveKit"
edition = "2021"
repository = "https://github.com/livekit/rust-sdks"

[features]
# By default ws TLS is not enabled
default = ["services-tokio", "access-token", "webhooks"]

signal-client-tokio = [
    "dep:tokio-tungstenite",
    "dep:tokio",
    "dep:futures-util",
    "dep:reqwest",
    "dep:livekit-runtime",
    "livekit-runtime/tokio",
    "dep:base64"
]

signal-client-async = [
    "__signal-client-async-compatible",
    "livekit-runtime/async"
]

signal-client-dispatcher = [
    "__signal-client-async-compatible",
    "livekit-runtime/dispatcher"
]

__signal-client-async-compatible = [
    "dep:async-tungstenite",
    "dep:tokio", # For macros and sync
    "dep:futures-util",
    "dep:isahc",
    "dep:livekit-runtime",
]


services-tokio = ["dep:reqwest"]
services-async = ["dep:isahc"]
access-token = ["dep:jsonwebtoken"]
webhooks = ["access-token", "dep:serde_json", "dep:base64"]

# Note that the following features only change the behavior of tokio-tungstenite.
# It doesn't change the behavior of libwebrtc/webrtc-sys
native-tls = [
    "tokio-tungstenite?/native-tls",
    "async-tungstenite?/async-native-tls",
    "reqwest?/native-tls"
]
native-tls-vendored = [
    "tokio-tungstenite?/native-tls-vendored",
    "reqwest?/native-tls-vendored",
]
rustls-tls-native-roots = [
    "tokio-tungstenite?/rustls-tls-native-roots",
    "reqwest?/rustls-tls-native-roots",
    "tokio-tungstenite?/__rustls-tls",
    "dep:tokio-rustls",
    "dep:rustls-native-certs"
]
rustls-tls-webpki-roots = [
    "tokio-tungstenite?/rustls-tls-webpki-roots",
    "reqwest?/rustls-tls-webpki-roots",
]
__rustls-tls = ["tokio-tungstenite?/__rustls-tls", "reqwest?/__rustls"]

[dependencies]
livekit-protocol = { workspace = true }
thiserror = "1.0"
serde = { version = "1.0", features = ["derive"] }
sha2 = "0.10"
url = "2.3"
log = "0.4"
parking_lot = { version = "0.12" }
prost = "0.12"
pbjson-types = "0.6"

# webhooks
serde_json = { version = "1.0", optional = true }
base64 = { version = "0.21", optional = true, features = ["std"] }

# access_token & services
jsonwebtoken = { version = "9", default-features = false, optional = true }

# signal_client
livekit-runtime = { workspace = true, optional = true}
tokio-tungstenite = { version = "0.20", optional = true }
async-tungstenite = { version = "0.25.0", features = [ "async-std-runtime", "async-native-tls"], optional = true }
tokio = { version = "1", default-features = false, features = ["sync", "macros", "signal", "io-util", "net"], optional = true }
tokio-rustls = { version = "0.24", optional = true }
rustls-native-certs = { version = "0.6", optional = true }
futures-util = { version = "0.3", default-features = false, features = [ "sink" ], optional = true }

# This dependency must be kept in sync with reqwest's version
http = "0.2.1"
reqwest = { version = "0.11", default-features = false, features = [ "json" ], optional = true }
isahc = { version = "1.7.2", default-features = false, features = [ "json", "text-decoding" ], optional = true }

scopeguard = "1.2.0"
rand = "0.9.0"
